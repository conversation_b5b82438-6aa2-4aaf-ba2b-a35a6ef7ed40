/*
  Localizable.strings
  NXC EV Solutions

  Created by Developer on 01/11/21.

*/

// MARK: - Menu
"Profile" = "Profile";
"Change Language" = "Change Language";
"Get Your RFID Card" = "Get Your RFID Card";
"News" = "News";
"Buy Charger" = "Buy Charger";
"Games" = "Games";
"Help" = "Help";
"Complaint" = "Complaint";
"About Us" = "About Us";
"Avail Balance :" = "Avail Balance :";

// MARK: - Filter
"Filter" = "Filter";
"By Charger Type" = "By Charger Type";
"By Connector Type" = "By Connector Type";
"By Ratings" = "By Ratings";
"Only Favourite" = "Only Favourite";
"Only Available Chargers" = "Only Available Chargers";
"& Up" = "& Up";
"Public" = "Public";
"Private" = "Private";

// MARK: - Wallet
"Wallet" = "Wallet";
"Available Balance" = "Available Balance";
"Add Money" = "Add Money";
"See Activity" = "See Activity";
"View More" = "View More";


// MARK: - History
"Filter By" = "Filter By";
"Select Vehicle" = "Select Vehicle";
"Select Date" = "Select Date";
"Proceed" = "Proceed";

"All" = "All";
"Paid For Charging" = "Paid for charging";
"Added To Wallet" = "Added To Wallet";
"Total Debited" = "Total Debited";
"Total Credited" = "Total Credited";
"No transactions available" = "No transactions available";

"Search Charge Station" = "Search Charge Station";


// MARK: - Add Money
"Add Money" = "Add Money";
"Enter Amount" = "Enter Amount";
"Enter Promo Code" = "Enter Promo Code";
"Apply" = "Apply";
"Note : Money should be added in multiples of 10" = "Note : Money should be added in multiples of 10";
"Please add amount in multiples of 10" = "Please add amount in multiples of 10";
"Promotions" = "Promotions";
"Terms & Conditions" = "Terms & Conditions";
"Done" = "Done";
"Please enter amount to continue with the transaction" = "Please enter amount to continue with the transaction";

// MARK: - Complaint Details
"Complaint Details" = "Complaint Details";
"Add Complaint" = "Add Complaint";
"Transaction ID" = "Transaction ID";
"Status" = "Status";
"Date" = "Date";
"Time" = "Time";
"Details" = "Details";
"Charging" = "Charging";
"Charger" = "Charger";
"Pending" = "Pending";
"InProgress" = "InProgress";
"Resolved" = "Resolved";
"Apply Filter" = "Apply Filter";
"No complaints available" = "No complaints available";

// MARK: - Add Complaint
"Select Complaint Type" = "Select Complaint Type";
"Select Transaction" = "Select Transaction";
"Enter Description" = "Enter Description";
"Submit" = "Submit";

"Complaint Type" = "Complaint Type";
"Please select complaint type" = "Please select complaint type";
"Transaction" = "Transaction";
"Please select transaction" = "Please select transaction";
"Description" = "Description";
"Please enter description" = "Please enter description";


// MARK: - RFID Card
"RFID Card" = "RFID Card";
"Issue New Card" = "Issue New Card";
"Track Order" = "Track Order";


// MARK: - Order RFID
"Order RFID" = "Order RFID";
"Full Name" = "Full Name";
"Phone Number" = "Phone Number";
"Flat, House No. , Building" = "Flat, House No. , Building";
"Area Street" = "Area Street";
"Landmark" = "Landmark";
"Pincode" = "Pincode";
"State" = "State";
"Town/City" = "City";
"RFID Card Charges : " = "RFID Card Charges : ";

"Order RFID Card" = "Order RFID Card";
"Please verify your address details." = "Please verify your address details.";
"will be charged from your wallet" = "will be charged from your wallet";
"Order Now" = "Order Now";

"Your RFID Card" = "Your RFID Card";
"No Records Found" = "No Records Found";

"You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number." = "You will receive your RFID Card in 8-10 working days, you will get updates regarding delivery on your registered number.";


"House No." = "House No.";
"Please enter flat/house no, buiding" = "Please enter flat/house no, buiding";
"Area, Street" = "Area, Street";
"Please enter area, street" = "Please enter area, street";
"Landmark" = "Landmark";
"Please enter landmark" = "Please enter landmark";
"Pincode" = "Pincode";
"Please enter pincode" = "Please enter pincode";
"State" = "State";
"Please enter state" = "Please enter state";
"Town/City" = "Town/City";
"Please enter town/city" = "Please enter town/city";
"Insufficient balance in you wallet." = "Insufficient balance in you wallet.";
"Please top-up your wallet." = "Please top-up your wallet.";
"Insufficient Balance" = "Insufficient Balance";
"Top-up Now" = "Top-up Now";
"Please verify your address details." = "Please verify your address details.";
" will be charged from your wallet." = " will be charged from your wallet.";
"Order Now" = "Order Now";

"Your RFID Order" = "Your RFID Order";
"No Records Found" = "No Records Found";


// MARK: - Profile
"User Profile" = "User Profile";
"Personal Details" = "Personal Details";
"Car Profile" = "Car Profile";
"Delete Account" = "Delete Account";
"Are you sure you want to delete your account?" = "Are you sure you want to delete your account?";
"You will lose all your data and your account will be permanently deleted." = "You will lose all your data and your account will be permanently deleted.";
"DELETE" = "DELETE";
"Refund Your Money" = "Refund Your Money";
"Please enter bank details to enter money in your wallet" = "Please enter bank details to enter money in your wallet";
"Your account will be permanently deleted" = "Your account will be permanently deleted";


// MARK: - Charging
"Start Charging" = "Start Charging";
"Stop Charging" = "Stop Charging";
"Please connect your vehicle" = "Please connect your vehicle";
"I have connected" = "I have connected";
"Cancel" = "Cancel";
"Stop Charging" = "Stop Charging";
"Start Time : " = "Start Time : ";
"Consumed Units" = "Consumed Units";
"Total Charges" = "Total Charges";

// MARK: - QR Code Manual Entry
"Manual Entry" = "Manual Entry";
"Enter QR Code" = "Enter QR Code";
"Submit" = "Submit";
"Invalid QR Code" = "Invalid QR Code";
"Please enter QR code" = "Please enter QR code";
"QR Code entered manually" = "QR Code entered manually";
"Enter QR code manually" = "Enter QR code manually";

// MARK: - Transaction Details
"Tax" = "Tax";

"SUCCESSFUL" = "SUCCESSFUL";
"Your transaction was successful" = "Your transaction was successful";
"Start Time" = "Start Time";
"Duration" = "Duration";
"Charge Point" = "Charge Point";
"Total Charges" = "Total Charges";
"More Info" = "More Info";

"How was your experience?" = "How was your experience?";
"Skip" = "Skip";
"Rate" = "Rate";

"Get Directions" = "Get Directions";
"EV Charging Station" = "EV Charging Station";
"Charge Station Timings" = "Charge Station Timings";


// MARK: - Logout
"Logout" = "Logout";
"Logout Message" = "Are you sure you want to logout?";
"YES" = "YES";
"NO" = "NO";

// MARK: - Login
"Login" = "Login";
"By logging in, you agree to our Terms and Conditions" = "By logging in, you agree to our Terms and Conditions";
"A NXC Controls Pvt. Ltd. Product" = "A NXC Controls Pvt. Ltd. Product";
"Please enter phone number" = "Please enter phone number";
"Please enter valid phone number" = "Please enter valid phone number";

// MARK: - Phone Verification
"Phone Verification" = "Phone Verification";
"Enter your 6-digit OTP code" = "Enter your 6-digit OTP code";
"VERIFY" = "VERIFY";
"Didn't receive the code? Resend Code" = "Didn't receive the code? Resend Code";











//
//"Map" = "Map";
//"Charging" = "Charging";
//"Nearby" = "Nearby";
//"Add Money" = "Add Money";
//
//"Login" = "Login";
//"Share" = "Share";
//"By Signing, you agree to our Terms and Conditions" = "By Signing, you agree to our Terms and Conditions";
//
//"Ratings" = "Ratings";
//"START CHARGING" = "START CHARGING";
//"STOP CHARGING" = "STOP CHARGING";
//
//"Enter Amount" = "Enter Amount";
//"Please enter amount to continue with the transaction" = "Please enter amount to continue with the transaction";
//"Payment Mode" = "Payment Mode";
//"Please add amount in multiples of 10" = "Please add amount in multiples of 10";
//"Please select payment mode to continue with the transaction" = "Please select payment mode to continue with the transaction";
//"Proceed To Pay" = "Proceed To Pay";
//
//
//"Wallet history not available" = "Wallet history not available";
//
//
//"Search Charge Station" = "Search Charge Station";
//
//"Please wait" = "Please wait";
//
//"Select Vehicle" = "Select Vehicle";
//
//"We can improve the accuracy of your sharing data if we know which vehicle you drive" = "We can improve the accuracy of your sharing data if we know which vehicle you drive";
//
//
//"Brand" = "Brand";
//"Select brand" = "Select Brand";
//"Please select brand" = "Please select brand";
//"Please enter other brand" = "Please enter other brand";
//
//"Type" = "Type";
//"Select type" = "Select type";
//"Please select type" = "Please select type";
//"Please enter other type" = "Please enter other type";
//
//"Model" = "Model";
//"Select model" = "Select model";
//"Please select model" = "Please select model";
//"Please enter other model" = "Please enter other model";
//
//"Enter registration number" = "Enter registration number";
//"Registration number" = "Registration number";
//"Please add registration number" = "Please add registration number";
//
//
//"Please select vehicle to start charging" = "Please select vehicle to start charging";
//
//
//"Please enter phone number" = "Please enter phone number";
//"Please enter valid phone number" = "Please enter valid phone number";
//
//
//"Please enter correct OTP" = "Please enter correct OTP";
//"Please enter valid OTP" = "Please enter valid OTP";
//
//
//"Not Available" = "Not Available";
//"This charger is not available for reservation" = "This charger is not available for reservation";
//
//"Access denied" = "Access denied पहुंच अस्वीकृत";
//
//"Please select gender to proceed" = "Please select gender to proceed";
//
//"First name" = "First name";
//"Please enter firstname" = "Please enter first name";
//
//"Last name" = "Last name";
//"Please enter last name" = "Please enter last name";
//
//"Date of birth" = "Date of birth";
//"Please select date of birth" = "Please select date of birth";
//
//"Gender" = "Gender";
//"Please select gender" = "Please select gender";
//
//"Please enter valid email address" = "Please enter valid email address";
//
//"Address" = "Address";
//"Please enter address" = "Please enter address";
//
//"Pincode" = "Pincode";
//"Please enter pincode" = "Please enter pincode";
//
//"Country" = "Country";
//"Please select country" = "Please select country";
//
//"State" = "State";
//"Please select state" = "Please select state";
//
//"City" = "City";
//"Please select city" = "Please select city";
//
//"Please enter only alphabetical letters" = "Please enter only alphabetical letters";
//
//"Name" = "Name";
//
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//
//"Please enter new password" = "Please enter new password";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//"Type" = "Type";
//"Please select complain type" = "Please select complain type";
//"Transaction" = "Transaction";
//"Please select transaction" = "Please select transaction";
//
//"Vehicle registration" = "Vehicle registration";
//"Vehicle registered successfully" = "Vehicle registered successfully";
//
//"You will get SMS with a confirmation OTP on this number" = "You will get SMS with a confirmation OTP on this number";
//"Search" = "Search";
//
//"Profile added" = "Profile added";
//"Your profile added successfully" = "Your profile added successfully";
//"Profile edited" = "Profile edited";
//"Your profile edited successfully" = "Your profile edited successfully";
//
//"Energy Charges Calculator" = "Energy Charges Calculator";
//"ENERGY CHARGES CALCULATOR" = "ENERGY CHARGES CALCULATOR";
//
//"Select complain type" = "Select complain type";
//"Select transaction" = "Select transaction";
//"Enter description" = "Enter description";
//
//"Resolved" = "Resolved";
//"All" = "All";
//
//"Time & Charges" = "Time & Charges";
//
//"Could not connect to the server" = "Could not connect to the server";
//"Please check your internet connection" = "Please check your internet connection";
//
//
//"First Name"="First Name";
//"Last Name"="Last Name";
//"Date Of Birth"="Date Of Birth";
//"Gender"="Gender";
//"Address"="Address";
//"Pincode"="Pincode";
//"Country"="Country";
//"State"="State";
//"City"="City";
//
//
//
