<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--ChargingVC-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController storyboardIdentifier="ChargingVC" id="Y6W-OH-hqX" customClass="ChargingVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="zTk-xP-luk">
                                <rect key="frame" x="15" y="113.5" width="345" height="440"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zHn-89-Fdq">
                                        <rect key="frame" x="77" y="0.0" width="191" height="191.5"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_animate_bg" translatesAutoresizingMaskIntoConstraints="NO" id="bEI-2k-TDE">
                                                <rect key="frame" x="0.0" y="0.0" width="191" height="191.5"/>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_car_wallet" translatesAutoresizingMaskIntoConstraints="NO" id="Fe7-nq-gtC">
                                                <rect key="frame" x="30" y="30" width="131" height="131.5"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="Fe7-nq-gtC" secondAttribute="height" multiplier="1:1" id="Lby-4B-s9d"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="Fe7-nq-gtC" firstAttribute="leading" secondItem="zHn-89-Fdq" secondAttribute="leading" constant="30" id="4Za-iZ-EIu"/>
                                            <constraint firstAttribute="bottom" secondItem="bEI-2k-TDE" secondAttribute="bottom" id="Eyh-q9-4HM"/>
                                            <constraint firstItem="bEI-2k-TDE" firstAttribute="top" secondItem="zHn-89-Fdq" secondAttribute="top" id="Ghj-if-jKy"/>
                                            <constraint firstItem="Fe7-nq-gtC" firstAttribute="top" secondItem="zHn-89-Fdq" secondAttribute="top" constant="30" id="Jm4-3i-J7D"/>
                                            <constraint firstAttribute="trailing" secondItem="Fe7-nq-gtC" secondAttribute="trailing" constant="30" id="MTT-A2-gxr"/>
                                            <constraint firstAttribute="bottom" secondItem="Fe7-nq-gtC" secondAttribute="bottom" constant="30" id="mIm-jx-2p9"/>
                                            <constraint firstAttribute="trailing" secondItem="bEI-2k-TDE" secondAttribute="trailing" id="pWf-Pq-L9x"/>
                                            <constraint firstItem="bEI-2k-TDE" firstAttribute="leading" secondItem="zHn-89-Fdq" secondAttribute="leading" id="rDU-bn-kdk"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qwt-UA-rSh">
                                        <rect key="frame" x="147.5" y="199.5" width="50" height="8"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="8" id="7Jf-P4-jhd"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="GTI-JN-EWT">
                                        <rect key="frame" x="24" y="215.5" width="297.5" height="224.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="%" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fha-vO-IlX">
                                                <rect key="frame" x="137" y="0.0" width="23.5" height="29"/>
                                                <fontDescription key="fontDescription" type="system" weight="heavy" pointSize="24"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="bcb-DV-E58">
                                                <rect key="frame" x="108.5" y="37" width="80.5" height="18"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_charge" translatesAutoresizingMaskIntoConstraints="NO" id="nlb-eP-fWQ">
                                                        <rect key="frame" x="0.0" y="0.0" width="18" height="18"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="18" id="MwT-bM-fzD"/>
                                                            <constraint firstAttribute="height" constant="18" id="w35-cd-pUq"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charging" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NzL-uN-OSK">
                                                        <rect key="frame" x="18" y="0.0" width="62.5" height="18"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Start Time : 00:15:30" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YtF-oT-272">
                                                <rect key="frame" x="76.5" y="63" width="144.5" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="24" id="5b7-2R-uVS"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                <color key="textColor" name="PrimarySelection"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wCn-sc-w3v">
                                                <rect key="frame" x="0.0" y="95" width="297.5" height="87"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="LHX-BA-ArK">
                                                        <rect key="frame" x="4" y="8" width="289.5" height="71"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="DcI-lX-aFD">
                                                                <rect key="frame" x="0.0" y="11.5" width="144.5" height="47.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="70 kwh" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mGJ-OX-H8p">
                                                                        <rect key="frame" x="46" y="0.0" width="52.5" height="19.5"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Consumed Units" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fKI-os-fsZ">
                                                                        <rect key="frame" x="18.5" y="23.5" width="107" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="ONX-33-aF8"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="dya-2a-xXk">
                                                                <rect key="frame" x="144.5" y="11.5" width="145" height="47.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="₹50" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="z1Z-om-Wb8">
                                                                        <rect key="frame" x="57.5" y="0.0" width="29.5" height="19.5"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total Charges" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PoV-CA-yfT">
                                                                        <rect key="frame" x="27.5" y="23.5" width="89.5" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="dww-q4-2d5"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" name="UserProfileBG"/>
                                                <constraints>
                                                    <constraint firstItem="LHX-BA-ArK" firstAttribute="top" secondItem="wCn-sc-w3v" secondAttribute="top" constant="8" id="0yG-Bi-H6n"/>
                                                    <constraint firstItem="LHX-BA-ArK" firstAttribute="leading" secondItem="wCn-sc-w3v" secondAttribute="leading" constant="4" id="21W-sl-eo7"/>
                                                    <constraint firstAttribute="bottom" secondItem="LHX-BA-ArK" secondAttribute="bottom" constant="8" id="QSp-kb-Mxf"/>
                                                    <constraint firstAttribute="trailing" secondItem="LHX-BA-ArK" secondAttribute="trailing" constant="4" id="qMB-Wq-Xo3"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QrJ-pv-Bck">
                                                <rect key="frame" x="123.5" y="190" width="50" height="8"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="8" id="7VP-pF-LeR"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tcf-53-8Ts">
                                                <rect key="frame" x="58.5" y="206" width="180" height="18.5"/>
                                                <attributedString key="attributedText">
                                                    <fragment content="Available">
                                                        <attributes>
                                                            <color key="NSColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" size="16" name="Arial-BoldMT"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content=" ">
                                                        <attributes>
                                                            <color key="NSColor" name="Primary"/>
                                                            <font key="NSFont" size="16" name="Arial-BoldMT"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="Balance">
                                                        <attributes>
                                                            <color key="NSColor" red="1" green="0.57810515169999999" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <font key="NSFont" size="16" name="Arial-BoldMT"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="  ">
                                                        <attributes>
                                                            <color key="NSColor" name="Primary"/>
                                                            <font key="NSFont" size="16" name="Arial-BoldMT"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="₹">
                                                        <attributes>
                                                            <color key="NSColor" name="Primary"/>
                                                            <font key="NSFont" size="16" name="Helvetica-Bold"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                    <fragment content="500">
                                                        <attributes>
                                                            <color key="NSColor" name="Primary"/>
                                                            <font key="NSFont" size="16" name="Arial-BoldMT"/>
                                                            <font key="NSOriginalFont" size="18" name="Arial-BoldMT"/>
                                                        </attributes>
                                                    </fragment>
                                                </attributedString>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="wCn-sc-w3v" firstAttribute="width" secondItem="GTI-JN-EWT" secondAttribute="width" id="4zD-1B-jlz"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3SM-bP-exd">
                                <rect key="frame" x="64" y="28" width="299" height="44"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="nHN-Ed-ifX">
                                        <rect key="frame" x="4" y="4" width="287" height="36"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Vehicle" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KUR-b0-aEG">
                                                <rect key="frame" x="0.0" y="9.5" width="264" height="17"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_down_arrow" translatesAutoresizingMaskIntoConstraints="NO" id="Db2-iz-Gja">
                                                <rect key="frame" x="272" y="10.5" width="15" height="15"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="15" id="LcW-eC-qW0"/>
                                                    <constraint firstAttribute="width" constant="15" id="oNb-Vr-Y6Y"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="nHN-Ed-ifX" secondAttribute="trailing" constant="8" id="Mvp-vH-8Uk"/>
                                    <constraint firstItem="nHN-Ed-ifX" firstAttribute="top" secondItem="3SM-bP-exd" secondAttribute="top" constant="4" id="SjG-y5-Xlb"/>
                                    <constraint firstAttribute="bottom" secondItem="nHN-Ed-ifX" secondAttribute="bottom" constant="4" id="bIT-vm-OTp"/>
                                    <constraint firstItem="nHN-Ed-ifX" firstAttribute="leading" secondItem="3SM-bP-exd" secondAttribute="leading" constant="4" id="gyx-ag-yyp"/>
                                    <constraint firstAttribute="height" constant="44" id="h40-y5-MnC"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mfZ-Ih-Yhj">
                                <rect key="frame" x="19" y="607" width="337.5" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="DAc-8D-Y2e"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Start Charging">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="chargingTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="rrq-Pq-JCG"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xwd-Qd-8Zi">
                                <rect key="frame" x="8" y="28" width="44" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="09y-Pp-iw2"/>
                                    <constraint firstAttribute="width" constant="44" id="2TA-Rd-mdf"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="8" minY="8" maxX="8" maxY="8"/>
                                <state key="normal" image="ic_menu"/>
                                <connections>
                                    <action selector="menuButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="8bM-xb-hRc"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fsi-O6-WzE">
                                <rect key="frame" x="64" y="28" width="299" height="44"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="selectVehicleTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="kfQ-3t-mZL"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Q6t-gd-mAQ">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mwp-P2-Cxg">
                                        <rect key="frame" x="0.0" y="0.0" width="300" height="647"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="F6g-RN-oUl">
                                                <rect key="frame" x="12" y="8" width="276" height="77.5"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="0qA-ds-Ila">
                                                        <rect key="frame" x="12" y="12" width="252" height="53.5"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="9MG-gw-uM8">
                                                                <rect key="frame" x="0.0" y="5" width="226" height="44"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="J" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z9v-Kq-8l6">
                                                                        <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                                                        <color key="backgroundColor" name="Primary"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="hWo-ld-rgq"/>
                                                                            <constraint firstAttribute="width" constant="44" id="tRR-4s-jVb"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="22"/>
                                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Bsu-0A-sdo">
                                                                        <rect key="frame" x="56" y="1" width="170" height="41.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Jobin Macwan" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C70-uh-AjW">
                                                                                <rect key="frame" x="0.0" y="0.0" width="170" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Avail Balance : ₹ 25000" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fWB-Wc-jiT">
                                                                                <rect key="frame" x="0.0" y="24.5" width="170" height="17"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <color key="textColor" name="Primary"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_logout" translatesAutoresizingMaskIntoConstraints="NO" id="KsS-1L-9BF">
                                                                <rect key="frame" x="228" y="15" width="24" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="24" id="PK5-81-NkZ"/>
                                                                    <constraint firstAttribute="height" constant="24" id="hfq-vN-K7V"/>
                                                                </constraints>
                                                            </imageView>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="0qA-ds-Ila" firstAttribute="top" secondItem="F6g-RN-oUl" secondAttribute="top" constant="12" id="PMU-kY-XND"/>
                                                    <constraint firstAttribute="bottom" secondItem="0qA-ds-Ila" secondAttribute="bottom" constant="12" id="UTK-x5-BSh"/>
                                                    <constraint firstItem="0qA-ds-Ila" firstAttribute="leading" secondItem="F6g-RN-oUl" secondAttribute="leading" constant="12" id="Vm4-Pn-zxB"/>
                                                    <constraint firstAttribute="trailing" secondItem="0qA-ds-Ila" secondAttribute="trailing" constant="12" id="tv3-Gc-zgp"/>
                                                </constraints>
                                            </view>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="Y2V-QK-ocX">
                                                <rect key="frame" x="12" y="97.5" width="276" height="128"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="128" id="30W-2y-8Jh"/>
                                                </constraints>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="cell" rowHeight="50" id="MPR-Mi-Ojf" customClass="MenuCell" customModule="NXC_EV_Solutions" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="276" height="50"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="MPR-Mi-Ojf" id="iPe-CM-2YC">
                                                            <rect key="frame" x="0.0" y="0.0" width="276" height="50"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tqd-rq-10x">
                                                                    <rect key="frame" x="0.0" y="4" width="276" height="42"/>
                                                                    <subviews>
                                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="adk-lv-KV1">
                                                                            <rect key="frame" x="12" y="2" width="258" height="38"/>
                                                                            <subviews>
                                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="NRM-8J-vvY">
                                                                                    <rect key="frame" x="0.0" y="10" width="18" height="18"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="18" id="dtL-qI-1xf"/>
                                                                                        <constraint firstAttribute="width" constant="18" id="s0r-oP-Ywb"/>
                                                                                    </constraints>
                                                                                </imageView>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Profile" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DsH-dA-7uw">
                                                                                    <rect key="frame" x="34" y="0.0" width="224" height="38"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="height" constant="44" id="brf-4N-yer"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                                                                    <nil key="textColor"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                        </stackView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="adk-lv-KV1" firstAttribute="leading" secondItem="tqd-rq-10x" secondAttribute="leading" constant="12" id="210-1J-hR8"/>
                                                                        <constraint firstAttribute="trailing" secondItem="adk-lv-KV1" secondAttribute="trailing" constant="6" id="Ifx-ug-zvV"/>
                                                                        <constraint firstItem="adk-lv-KV1" firstAttribute="top" secondItem="tqd-rq-10x" secondAttribute="top" constant="2" id="fyj-QM-TY1"/>
                                                                        <constraint firstAttribute="bottom" secondItem="adk-lv-KV1" secondAttribute="bottom" constant="2" id="lFd-HF-dKu"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="tqd-rq-10x" firstAttribute="top" secondItem="iPe-CM-2YC" secondAttribute="top" constant="4" id="11H-gn-9E9"/>
                                                                <constraint firstAttribute="bottom" secondItem="tqd-rq-10x" secondAttribute="bottom" constant="4" id="K4U-eH-1Gq"/>
                                                                <constraint firstAttribute="trailing" secondItem="tqd-rq-10x" secondAttribute="trailing" id="xmi-Bv-BFP"/>
                                                                <constraint firstItem="tqd-rq-10x" firstAttribute="leading" secondItem="iPe-CM-2YC" secondAttribute="leading" id="zne-eR-Zfa"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <connections>
                                                            <outlet property="imgMenu" destination="NRM-8J-vvY" id="2Ql-rP-pQN"/>
                                                            <outlet property="lblMenu" destination="DsH-dA-7uw" id="WUU-9J-nwP"/>
                                                            <outlet property="viewMain" destination="tqd-rq-10x" id="1mj-IW-iUs"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OAB-UF-vA3">
                                                <rect key="frame" x="242" y="25" width="44" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="OAB-UF-vA3" secondAttribute="height" multiplier="1:1" id="Vwf-Gw-eLp"/>
                                                    <constraint firstAttribute="height" constant="44" id="mDv-kI-Yn5"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="logoutTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="4LG-dk-xrv"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="Y2V-QK-ocX" firstAttribute="leading" secondItem="mwp-P2-Cxg" secondAttribute="leading" constant="12" id="1Zs-Df-fJc"/>
                                            <constraint firstItem="F6g-RN-oUl" firstAttribute="top" secondItem="mwp-P2-Cxg" secondAttribute="top" constant="8" id="3Gj-vV-D6W"/>
                                            <constraint firstItem="OAB-UF-vA3" firstAttribute="centerX" secondItem="KsS-1L-9BF" secondAttribute="centerX" id="7Sm-9G-V1s"/>
                                            <constraint firstAttribute="trailing" secondItem="Y2V-QK-ocX" secondAttribute="trailing" constant="12" id="93c-h5-1Ls"/>
                                            <constraint firstItem="F6g-RN-oUl" firstAttribute="height" secondItem="mwp-P2-Cxg" secondAttribute="height" multiplier="0.12" id="9hy-yL-T9E"/>
                                            <constraint firstItem="Y2V-QK-ocX" firstAttribute="top" secondItem="F6g-RN-oUl" secondAttribute="bottom" constant="12" id="AX6-09-Sfr"/>
                                            <constraint firstItem="OAB-UF-vA3" firstAttribute="centerY" secondItem="KsS-1L-9BF" secondAttribute="centerY" id="Gcz-E5-8eN"/>
                                            <constraint firstItem="F6g-RN-oUl" firstAttribute="leading" secondItem="mwp-P2-Cxg" secondAttribute="leading" constant="12" id="Me7-b8-h4Y"/>
                                            <constraint firstAttribute="trailing" secondItem="F6g-RN-oUl" secondAttribute="trailing" constant="12" id="Vht-kb-luf"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.75" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="mwp-P2-Cxg" firstAttribute="top" secondItem="Q6t-gd-mAQ" secondAttribute="top" id="Oae-Ac-Txg"/>
                                    <constraint firstAttribute="bottom" secondItem="mwp-P2-Cxg" secondAttribute="bottom" id="toO-z3-ItR"/>
                                    <constraint firstItem="mwp-P2-Cxg" firstAttribute="width" secondItem="Q6t-gd-mAQ" secondAttribute="width" multiplier="0.8" id="u8B-2n-5ZH"/>
                                    <constraint firstItem="mwp-P2-Cxg" firstAttribute="leading" secondItem="Q6t-gd-mAQ" secondAttribute="leading" id="y11-D2-eTO"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0iD-HJ-nBq">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hk0-Ou-lGW">
                                        <rect key="frame" x="19" y="126" width="337.5" height="395.5"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="c5z-Sg-H6N">
                                                <rect key="frame" x="16" y="151.5" width="305.5" height="168"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="iLC-Lw-Aqd">
                                                        <rect key="frame" x="0.0" y="0.0" width="305.5" height="74"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="dp8-rs-t4U">
                                                                <rect key="frame" x="0.0" y="0.0" width="148.5" height="74"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Start Time" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bDR-Yj-Nmb">
                                                                        <rect key="frame" x="0.0" y="0.0" width="148.5" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="6BU-TF-mKL"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mqv-3u-0Ij">
                                                                        <rect key="frame" x="0.0" y="24" width="148.5" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="9Gc-hb-hJL">
                                                                <rect key="frame" x="156.5" y="0.0" width="149" height="74"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Duration" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vmg-DS-aGw">
                                                                        <rect key="frame" x="0.0" y="0.0" width="149" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="i4m-bu-YKr"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6rS-xC-mfL">
                                                                        <rect key="frame" x="0.0" y="24" width="149" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Kh7-hw-pKa">
                                                        <rect key="frame" x="0.0" y="94" width="305.5" height="74"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="2cu-wq-uKs">
                                                                <rect key="frame" x="0.0" y="0.0" width="152.5" height="24"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charge Point" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="082-bf-cB7">
                                                                        <rect key="frame" x="0.0" y="0.0" width="152.5" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="5os-j2-kQo"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Pr-qQ-YKc">
                                                                        <rect key="frame" x="0.0" y="24" width="152.5" height="0.0"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="rU5-8w-GTZ">
                                                                <rect key="frame" x="160.5" y="0.0" width="145" height="74"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Total Charges" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bt7-U3-QbP">
                                                                        <rect key="frame" x="0.0" y="0.0" width="145" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="24" id="iKJ-Ur-Om8"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <color key="textColor" name="PrimarySelection"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iFx-CS-gyo">
                                                                        <rect key="frame" x="0.0" y="24" width="145" height="50"/>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="2cu-wq-uKs" firstAttribute="width" secondItem="Kh7-hw-pKa" secondAttribute="width" multiplier="0.5" id="vVg-IS-v27"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bqf-Bu-iWE">
                                                <rect key="frame" x="131" y="8" width="75" height="75"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_transaction_ successful" translatesAutoresizingMaskIntoConstraints="NO" id="TYo-uJ-wXc">
                                                        <rect key="frame" x="4" y="4" width="67" height="67"/>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="TYo-uJ-wXc" secondAttribute="bottom" constant="4" id="0gH-Yx-sDV"/>
                                                    <constraint firstItem="TYo-uJ-wXc" firstAttribute="top" secondItem="bqf-Bu-iWE" secondAttribute="top" constant="4" id="Ghk-LR-wxD"/>
                                                    <constraint firstAttribute="height" constant="75" id="Li4-3M-lil"/>
                                                    <constraint firstAttribute="trailing" secondItem="TYo-uJ-wXc" secondAttribute="trailing" constant="4" id="XcN-Xb-ea5"/>
                                                    <constraint firstAttribute="width" constant="75" id="fFm-Uj-HsJ"/>
                                                    <constraint firstItem="TYo-uJ-wXc" firstAttribute="leading" secondItem="bqf-Bu-iWE" secondAttribute="leading" constant="4" id="tiw-SM-iZJ"/>
                                                </constraints>
                                            </view>
                                            <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WrX-Xq-MVv">
                                                <rect key="frame" x="295.5" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="6y3-xF-gi6"/>
                                                    <constraint firstAttribute="height" constant="34" id="c8h-rc-kQt"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="closeTransactionTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="9Os-tN-KnC"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="hmU-JH-ucZ">
                                                <rect key="frame" x="65" y="95" width="207.5" height="40.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SUCCESSFUL!" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S3i-jN-Jkz">
                                                        <rect key="frame" x="0.0" y="0.0" width="207.5" height="19.5"/>
                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Your transaction was successful" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vsg-k0-rrS">
                                                        <rect key="frame" x="0.0" y="23.5" width="207.5" height="17"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="18k-aZ-MVP">
                                                <rect key="frame" x="16.5" y="335.5" width="304" height="44"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WKr-bO-A8d">
                                                        <rect key="frame" x="0.0" y="0.0" width="144" height="44"/>
                                                        <color key="backgroundColor" name="Primary"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="W7a-5h-9o5"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <state key="normal" title="Cancel">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="cancelTransactionPopTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="Gre-Bv-S1k"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="l61-qb-pgg">
                                                        <rect key="frame" x="160" y="0.0" width="144" height="44"/>
                                                        <color key="backgroundColor" name="Primary"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="KPA-DM-qrL"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <state key="normal" title="More Info">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="viewTransactionTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="RCm-qY-y0W"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="c5z-Sg-H6N" firstAttribute="top" secondItem="hmU-JH-ucZ" secondAttribute="bottom" constant="16" id="1Hg-cV-lnl"/>
                                            <constraint firstItem="18k-aZ-MVP" firstAttribute="width" secondItem="hk0-Ou-lGW" secondAttribute="width" multiplier="0.9" id="5hf-qo-xni"/>
                                            <constraint firstAttribute="bottom" secondItem="18k-aZ-MVP" secondAttribute="bottom" constant="16" id="B40-bT-Qfg"/>
                                            <constraint firstItem="18k-aZ-MVP" firstAttribute="centerX" secondItem="hk0-Ou-lGW" secondAttribute="centerX" id="Gfk-Nq-CWl"/>
                                            <constraint firstAttribute="trailing" secondItem="WrX-Xq-MVv" secondAttribute="trailing" constant="8" id="NcB-MY-sZp"/>
                                            <constraint firstItem="hmU-JH-ucZ" firstAttribute="centerX" secondItem="hk0-Ou-lGW" secondAttribute="centerX" id="TFA-Re-xIV"/>
                                            <constraint firstAttribute="trailing" secondItem="c5z-Sg-H6N" secondAttribute="trailing" constant="16" id="Tbu-fS-icK"/>
                                            <constraint firstItem="c5z-Sg-H6N" firstAttribute="leading" secondItem="hk0-Ou-lGW" secondAttribute="leading" constant="16" id="VFH-nV-CWr"/>
                                            <constraint firstItem="bqf-Bu-iWE" firstAttribute="centerX" secondItem="hk0-Ou-lGW" secondAttribute="centerX" id="ePR-vd-xuN"/>
                                            <constraint firstItem="WrX-Xq-MVv" firstAttribute="top" secondItem="hk0-Ou-lGW" secondAttribute="top" constant="8" id="fII-Pa-oKb"/>
                                            <constraint firstItem="bqf-Bu-iWE" firstAttribute="top" secondItem="hk0-Ou-lGW" secondAttribute="top" constant="8" id="mqE-A1-wOg"/>
                                            <constraint firstItem="18k-aZ-MVP" firstAttribute="top" secondItem="c5z-Sg-H6N" secondAttribute="bottom" constant="16" id="nXd-aH-CPK"/>
                                            <constraint firstItem="hmU-JH-ucZ" firstAttribute="top" secondItem="bqf-Bu-iWE" secondAttribute="bottom" constant="12" id="oIm-12-CyT"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mwf-F3-6mD">
                                        <rect key="frame" x="19" y="183" width="337.5" height="281.5"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QTm-mV-a9C">
                                                <rect key="frame" x="131" y="8" width="75" height="75"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_ratings-1" translatesAutoresizingMaskIntoConstraints="NO" id="CGl-zu-akN">
                                                        <rect key="frame" x="4" y="4" width="67" height="67"/>
                                                    </imageView>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstItem="CGl-zu-akN" firstAttribute="top" secondItem="QTm-mV-a9C" secondAttribute="top" constant="4" id="6Nv-01-NWE"/>
                                                    <constraint firstAttribute="width" constant="75" id="OJi-IK-lzO"/>
                                                    <constraint firstAttribute="height" constant="75" id="QC4-uf-dzR"/>
                                                    <constraint firstItem="CGl-zu-akN" firstAttribute="leading" secondItem="QTm-mV-a9C" secondAttribute="leading" constant="4" id="RqC-rH-cQY"/>
                                                    <constraint firstAttribute="trailing" secondItem="CGl-zu-akN" secondAttribute="trailing" constant="4" id="gVf-Ab-wFR"/>
                                                    <constraint firstAttribute="bottom" secondItem="CGl-zu-akN" secondAttribute="bottom" constant="4" id="pZR-Jj-kN8"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kgH-ad-hK3" customClass="CosmosView" customModule="Cosmos">
                                                <rect key="frame" x="42" y="155.5" width="253" height="50"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="cHf-gY-aT1"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="rating">
                                                        <real key="value" value="3"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="starSize">
                                                        <real key="value" value="46"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="fillMode">
                                                        <integer key="value" value="1"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="filledColor">
                                                        <color key="value" systemColor="systemYellowColor"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="emptyColor">
                                                        <color key="value" systemColor="opaqueSeparatorColor"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="emptyBorderColor">
                                                        <color key="value" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="starMargin">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5B0-Ts-M8z">
                                                <rect key="frame" x="295.5" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="Mpb-UV-GEA"/>
                                                    <constraint firstAttribute="width" constant="34" id="Q97-yJ-uB9"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="closeRatingsTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="dvL-ow-1jy"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="JMt-yX-oaU">
                                                <rect key="frame" x="68" y="95" width="201.5" height="44.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Charging Station Name" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MWx-1O-LaE">
                                                        <rect key="frame" x="0.0" y="0.0" width="201.5" height="17"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <nil key="textColor"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="How was your experience?" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z1s-qz-rVd">
                                                        <rect key="frame" x="0.0" y="25" width="201.5" height="19.5"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                        <color key="textColor" name="Primary"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="JZN-dc-nIj">
                                                <rect key="frame" x="16.5" y="221.5" width="304" height="44"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="knq-GK-E26">
                                                        <rect key="frame" x="0.0" y="0.0" width="144" height="44"/>
                                                        <color key="backgroundColor" name="Primary"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="LFm-hi-o93"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <state key="normal" title="Skip">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="skipRatingsButton:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="9xD-Gg-UrZ"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tMK-hJ-AwD">
                                                        <rect key="frame" x="160" y="0.0" width="144" height="44"/>
                                                        <color key="backgroundColor" name="Primary"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="nRq-H4-HbH"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <state key="normal" title="Rate">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="ratingsButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="hp9-QT-ZvG"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="JZN-dc-nIj" firstAttribute="width" secondItem="mwf-F3-6mD" secondAttribute="width" multiplier="0.9" id="00o-LP-edG"/>
                                            <constraint firstItem="JMt-yX-oaU" firstAttribute="top" secondItem="QTm-mV-a9C" secondAttribute="bottom" constant="12" id="20i-L1-UCU"/>
                                            <constraint firstItem="kgH-ad-hK3" firstAttribute="centerX" secondItem="mwf-F3-6mD" secondAttribute="centerX" id="B4A-Mg-BbP"/>
                                            <constraint firstItem="kgH-ad-hK3" firstAttribute="top" secondItem="JMt-yX-oaU" secondAttribute="bottom" constant="16" id="Nr9-hW-8iU"/>
                                            <constraint firstItem="QTm-mV-a9C" firstAttribute="top" secondItem="mwf-F3-6mD" secondAttribute="top" constant="8" id="Sff-yr-IBp"/>
                                            <constraint firstItem="JMt-yX-oaU" firstAttribute="centerX" secondItem="mwf-F3-6mD" secondAttribute="centerX" id="Uvu-Ib-TdK"/>
                                            <constraint firstItem="kgH-ad-hK3" firstAttribute="width" secondItem="mwf-F3-6mD" secondAttribute="width" multiplier="0.75" id="VsC-3q-jmQ"/>
                                            <constraint firstItem="QTm-mV-a9C" firstAttribute="centerX" secondItem="mwf-F3-6mD" secondAttribute="centerX" id="buW-I9-iFV"/>
                                            <constraint firstAttribute="trailing" secondItem="5B0-Ts-M8z" secondAttribute="trailing" constant="8" id="cGv-If-YFq"/>
                                            <constraint firstItem="JZN-dc-nIj" firstAttribute="top" secondItem="kgH-ad-hK3" secondAttribute="bottom" constant="16" id="gEB-Be-8Vy"/>
                                            <constraint firstItem="5B0-Ts-M8z" firstAttribute="top" secondItem="mwf-F3-6mD" secondAttribute="top" constant="8" id="lZ8-0q-W5w"/>
                                            <constraint firstItem="JZN-dc-nIj" firstAttribute="centerX" secondItem="mwf-F3-6mD" secondAttribute="centerX" id="pet-wu-NP7"/>
                                            <constraint firstAttribute="bottom" secondItem="JZN-dc-nIj" secondAttribute="bottom" constant="16" id="zBD-oV-06t"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="hk0-Ou-lGW" firstAttribute="centerY" secondItem="0iD-HJ-nBq" secondAttribute="centerY" id="67r-Tg-NI2"/>
                                    <constraint firstItem="hk0-Ou-lGW" firstAttribute="centerX" secondItem="0iD-HJ-nBq" secondAttribute="centerX" id="KYl-Ge-ehF"/>
                                    <constraint firstItem="mwf-F3-6mD" firstAttribute="centerY" secondItem="0iD-HJ-nBq" secondAttribute="centerY" id="KsA-WD-bu6"/>
                                    <constraint firstItem="mwf-F3-6mD" firstAttribute="width" secondItem="0iD-HJ-nBq" secondAttribute="width" multiplier="0.9" id="R6O-lg-EbJ"/>
                                    <constraint firstItem="mwf-F3-6mD" firstAttribute="centerX" secondItem="0iD-HJ-nBq" secondAttribute="centerX" id="fUo-BN-ca1"/>
                                    <constraint firstItem="hk0-Ou-lGW" firstAttribute="width" secondItem="0iD-HJ-nBq" secondAttribute="width" multiplier="0.9" id="rtk-Rp-Gt9"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sjm-cj-hYg">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1uA-g0-h6Z">
                                        <rect key="frame" x="19" y="120.5" width="337.5" height="406"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Select Charge Type" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eEg-N1-h2R">
                                                <rect key="frame" x="91" y="16" width="155.5" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                <color key="textColor" name="Primary"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4nL-B5-RsF">
                                                <rect key="frame" x="295.5" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="34" id="GvV-vF-QHw"/>
                                                    <constraint firstAttribute="height" constant="34" id="Qbb-Sm-U9f"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                                <connections>
                                                    <action selector="chargeTypeCloseTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="mBq-FU-y7j"/>
                                                </connections>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="8qd-GL-0iS">
                                                <rect key="frame" x="16.5" y="58" width="304" height="332"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="oXG-V3-SZF">
                                                        <rect key="frame" x="0.0" y="0.0" width="304" height="34"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="idf-9n-b8k">
                                                                <rect key="frame" x="0.0" y="0.0" width="128" height="34"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Y2f-HU-HaS">
                                                                        <rect key="frame" x="8" y="4" width="112" height="26"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_full_charge" translatesAutoresizingMaskIntoConstraints="NO" id="cP0-Cl-xi2">
                                                                                <rect key="frame" x="0.0" y="4" width="18" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="18" id="Gsx-Be-53d"/>
                                                                                    <constraint firstAttribute="width" constant="18" id="KmE-SD-BmX"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Full Charged" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EoZ-a0-xvq">
                                                                                <rect key="frame" x="22" y="0.0" width="90" height="26"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="90" id="9kb-Ee-BEI"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="EoZ-a0-xvq" firstAttribute="height" secondItem="Y2f-HU-HaS" secondAttribute="height" id="t74-ir-Wrb"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="bottom" secondItem="Y2f-HU-HaS" secondAttribute="bottom" constant="4" id="KdN-3X-nxK"/>
                                                                    <constraint firstItem="Y2f-HU-HaS" firstAttribute="leading" secondItem="idf-9n-b8k" secondAttribute="leading" constant="8" id="V94-OZ-aRW"/>
                                                                    <constraint firstAttribute="height" constant="34" id="iBh-fW-Nbi"/>
                                                                    <constraint firstItem="Y2f-HU-HaS" firstAttribute="top" secondItem="idf-9n-b8k" secondAttribute="top" constant="4" id="nyi-1S-Ujj"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Y2f-HU-HaS" secondAttribute="trailing" constant="8" id="pYm-84-RF3"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mHn-gQ-4Io">
                                                                <rect key="frame" x="136" y="0.0" width="78" height="34"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="YTe-vd-kSf">
                                                                        <rect key="frame" x="8" y="4" width="62" height="26"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_unite" translatesAutoresizingMaskIntoConstraints="NO" id="xrh-3q-SIg">
                                                                                <rect key="frame" x="0.0" y="4" width="18" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="18" id="huP-i7-Wx0"/>
                                                                                    <constraint firstAttribute="width" constant="18" id="tjY-CB-tE1"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Unit" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bxk-xN-JhT">
                                                                                <rect key="frame" x="22" y="0.0" width="40" height="26"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="40" id="6ya-iY-zrK"/>
                                                                                </constraints>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="bxk-xN-JhT" firstAttribute="height" secondItem="YTe-vd-kSf" secondAttribute="height" id="ux5-1a-rjv"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="YTe-vd-kSf" secondAttribute="trailing" constant="8" id="2YA-kX-76Z"/>
                                                                    <constraint firstItem="YTe-vd-kSf" firstAttribute="leading" secondItem="mHn-gQ-4Io" secondAttribute="leading" constant="8" id="3EN-eV-Bbe"/>
                                                                    <constraint firstItem="YTe-vd-kSf" firstAttribute="top" secondItem="mHn-gQ-4Io" secondAttribute="top" constant="4" id="912-ql-8YO"/>
                                                                    <constraint firstAttribute="height" constant="34" id="Lvb-qP-sSz"/>
                                                                    <constraint firstAttribute="bottom" secondItem="YTe-vd-kSf" secondAttribute="bottom" constant="4" id="hI9-jU-YUp"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hxQ-5D-7cW">
                                                                <rect key="frame" x="222" y="0.0" width="82" height="34"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="0dg-GQ-ezJ">
                                                                        <rect key="frame" x="8" y="4" width="66" height="26"/>
                                                                        <subviews>
                                                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_price" translatesAutoresizingMaskIntoConstraints="NO" id="3SU-lW-rW0">
                                                                                <rect key="frame" x="0.0" y="4" width="18" height="18"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="18" id="0Tr-Wg-YD6"/>
                                                                                    <constraint firstAttribute="height" constant="18" id="F2B-HU-PF5"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g2B-in-pgg">
                                                                                <rect key="frame" x="22" y="0.0" width="44" height="26"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                                <nil key="textColor"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                        <constraints>
                                                                            <constraint firstItem="g2B-in-pgg" firstAttribute="height" secondItem="0dg-GQ-ezJ" secondAttribute="height" id="cZ6-Ul-dLm"/>
                                                                        </constraints>
                                                                    </stackView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="0dg-GQ-ezJ" firstAttribute="leading" secondItem="hxQ-5D-7cW" secondAttribute="leading" constant="8" id="FcJ-NP-Sd6"/>
                                                                    <constraint firstAttribute="trailing" secondItem="0dg-GQ-ezJ" secondAttribute="trailing" constant="8" id="J6z-EN-eVo"/>
                                                                    <constraint firstItem="0dg-GQ-ezJ" firstAttribute="top" secondItem="hxQ-5D-7cW" secondAttribute="top" constant="4" id="MTE-VL-8xz"/>
                                                                    <constraint firstAttribute="bottom" secondItem="0dg-GQ-ezJ" secondAttribute="bottom" constant="4" id="U1l-1b-kYf"/>
                                                                    <constraint firstAttribute="height" constant="34" id="ayA-eZ-Lpd"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="tti-HO-gou">
                                                        <rect key="frame" x="0.0" y="42" width="304" height="238"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="URu-wS-8nJ">
                                                                <rect key="frame" x="0.0" y="0.0" width="304" height="238"/>
                                                                <subviews>
                                                                    <datePicker contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" datePickerMode="time" style="wheels" translatesAutoresizingMaskIntoConstraints="NO" id="NNh-jR-CIk">
                                                                        <rect key="frame" x="0.0" y="0.0" width="304" height="150"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="150" id="Cm8-lG-4RH"/>
                                                                        </constraints>
                                                                    </datePicker>
                                                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="KEn-3I-7jE">
                                                                        <rect key="frame" x="0.0" y="162" width="304" height="44"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="44" id="Wzd-2m-SyG"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                        <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                                                    </textField>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3Hb-Eq-0qR">
                                                                        <rect key="frame" x="0.0" y="218" width="304" height="20"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="20" id="W5q-oT-74K"/>
                                                                        </constraints>
                                                                        <attributedString key="attributedText">
                                                                            <fragment content="*">
                                                                                <attributes>
                                                                                    <color key="NSColor" red="1" green="0.14913141730000001" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                    <font key="NSFont" metaFont="system" size="15"/>
                                                                                    <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                                </attributes>
                                                                            </fragment>
                                                                            <fragment content=" Estimated Price  ">
                                                                                <attributes>
                                                                                    <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                                                    <font key="NSFont" metaFont="system" size="15"/>
                                                                                    <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                                </attributes>
                                                                            </fragment>
                                                                            <fragment content="≈ ₹150">
                                                                                <attributes>
                                                                                    <color key="NSColor" red="0.015686274509803921" green="0.49411764705882355" blue="0.42745098039215684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                    <font key="NSFont" metaFont="system" size="15"/>
                                                                                    <paragraphStyle key="NSParagraphStyle" alignment="left" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                                                </attributes>
                                                                            </fragment>
                                                                        </attributedString>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <constraints>
                                                                    <constraint firstItem="NNh-jR-CIk" firstAttribute="width" secondItem="URu-wS-8nJ" secondAttribute="width" id="1yp-zf-Vv1"/>
                                                                    <constraint firstItem="3Hb-Eq-0qR" firstAttribute="width" secondItem="URu-wS-8nJ" secondAttribute="width" id="T9O-th-pdx"/>
                                                                    <constraint firstItem="KEn-3I-7jE" firstAttribute="width" secondItem="URu-wS-8nJ" secondAttribute="width" id="bTf-FF-SB7"/>
                                                                </constraints>
                                                            </stackView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="URu-wS-8nJ" firstAttribute="width" secondItem="tti-HO-gou" secondAttribute="width" id="ba0-22-VNA"/>
                                                        </constraints>
                                                    </stackView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6je-cS-yf2">
                                                        <rect key="frame" x="0.0" y="288" width="304" height="44"/>
                                                        <color key="backgroundColor" name="Primary"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="44" id="iRA-3P-qMX"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                        <state key="normal" title="Scan QR Code">
                                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="scanQRTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="uLo-sE-Vgj"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <button opaque="NO" tag="101" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rF7-Sw-y7N">
                                                <rect key="frame" x="16.5" y="53" width="128" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="zC9-V8-Tbk"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="chargeTypeTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="Ncg-pE-tow"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="102" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9ZS-hY-Gwa">
                                                <rect key="frame" x="152.5" y="53" width="78" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="WfX-pg-ae2"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="chargeTypeTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="5Fc-tw-9xJ"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" tag="103" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gad-v1-efO">
                                                <rect key="frame" x="238.5" y="53" width="82" height="44"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="44" id="a2F-4M-BoH"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="chargeTypeTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="HGB-kJ-Hal"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="8qd-GL-0iS" firstAttribute="top" secondItem="4nL-B5-RsF" secondAttribute="bottom" constant="16" id="0IR-4N-1Od"/>
                                            <constraint firstAttribute="trailing" secondItem="4nL-B5-RsF" secondAttribute="trailing" constant="8" id="0Wu-AF-Yaw"/>
                                            <constraint firstItem="rF7-Sw-y7N" firstAttribute="centerY" secondItem="idf-9n-b8k" secondAttribute="centerY" id="1Qe-pe-lJt"/>
                                            <constraint firstItem="eEg-N1-h2R" firstAttribute="top" secondItem="1uA-g0-h6Z" secondAttribute="top" constant="16" id="1iQ-jm-BDp"/>
                                            <constraint firstAttribute="bottom" secondItem="8qd-GL-0iS" secondAttribute="bottom" constant="16" id="4uI-nZ-lhD"/>
                                            <constraint firstItem="9ZS-hY-Gwa" firstAttribute="centerX" secondItem="mHn-gQ-4Io" secondAttribute="centerX" id="99Z-7B-88l"/>
                                            <constraint firstItem="4nL-B5-RsF" firstAttribute="top" secondItem="1uA-g0-h6Z" secondAttribute="top" constant="8" id="Ecv-kH-MgW"/>
                                            <constraint firstItem="rF7-Sw-y7N" firstAttribute="width" secondItem="idf-9n-b8k" secondAttribute="width" id="O8O-Zu-N1e"/>
                                            <constraint firstItem="8qd-GL-0iS" firstAttribute="centerX" secondItem="1uA-g0-h6Z" secondAttribute="centerX" id="RWX-h3-E4Q"/>
                                            <constraint firstItem="9ZS-hY-Gwa" firstAttribute="width" secondItem="mHn-gQ-4Io" secondAttribute="width" id="SLA-cY-jRa"/>
                                            <constraint firstItem="9ZS-hY-Gwa" firstAttribute="centerY" secondItem="mHn-gQ-4Io" secondAttribute="centerY" id="Wb1-ff-YVT"/>
                                            <constraint firstItem="gad-v1-efO" firstAttribute="width" secondItem="hxQ-5D-7cW" secondAttribute="width" id="XQ4-cy-L4N"/>
                                            <constraint firstItem="gad-v1-efO" firstAttribute="centerX" secondItem="hxQ-5D-7cW" secondAttribute="centerX" id="d4i-OQ-rob"/>
                                            <constraint firstItem="gad-v1-efO" firstAttribute="centerY" secondItem="hxQ-5D-7cW" secondAttribute="centerY" id="lOn-92-6g4"/>
                                            <constraint firstItem="rF7-Sw-y7N" firstAttribute="centerX" secondItem="idf-9n-b8k" secondAttribute="centerX" id="mby-CF-4Pn"/>
                                            <constraint firstItem="eEg-N1-h2R" firstAttribute="centerX" secondItem="1uA-g0-h6Z" secondAttribute="centerX" id="sNO-nI-qRZ"/>
                                            <constraint firstItem="8qd-GL-0iS" firstAttribute="width" secondItem="1uA-g0-h6Z" secondAttribute="width" multiplier="0.9" id="v6X-vj-cHG"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="kWh" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s0v-qr-wpf">
                                        <rect key="frame" x="289.5" y="396" width="50" height="17"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="50" id="4dR-xx-uDV"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="1uA-g0-h6Z" firstAttribute="centerY" secondItem="Sjm-cj-hYg" secondAttribute="centerY" id="1kP-7P-uPN"/>
                                    <constraint firstItem="1uA-g0-h6Z" firstAttribute="width" secondItem="Sjm-cj-hYg" secondAttribute="width" multiplier="0.9" id="HmV-mr-nJ4"/>
                                    <constraint firstItem="s0v-qr-wpf" firstAttribute="centerY" secondItem="KEn-3I-7jE" secondAttribute="centerY" id="QIS-eC-ITI"/>
                                    <constraint firstItem="1uA-g0-h6Z" firstAttribute="centerX" secondItem="Sjm-cj-hYg" secondAttribute="centerX" id="aXi-oH-v0i"/>
                                    <constraint firstItem="s0v-qr-wpf" firstAttribute="trailing" secondItem="KEn-3I-7jE" secondAttribute="trailing" id="aqU-5v-4WV"/>
                                </constraints>
                            </view>
                            <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CeV-SO-fEM">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eRZ-Ct-N2q">
                                        <rect key="frame" x="0.0" y="97" width="375" height="550"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please connect your vehicle" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YxW-j1-7Q7">
                                                <rect key="frame" x="84" y="30" width="207.5" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="vwb-7S-M01"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                <color key="textColor" name="PrimaryTextLight"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" alpha="0.0" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8UL-Ca-KTq">
                                                <rect key="frame" x="333" y="8" width="34" height="34"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="34" id="KYl-Tf-TIh"/>
                                                    <constraint firstAttribute="width" constant="34" id="x4y-wL-tap"/>
                                                </constraints>
                                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                                <state key="normal" image="ic_cancel"/>
                                            </button>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="SiI-IS-77L">
                                                <rect key="frame" x="19" y="94" width="337.5" height="426"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_plug_connected" translatesAutoresizingMaskIntoConstraints="NO" id="mJg-sE-6b8">
                                                        <rect key="frame" x="43.5" y="0.0" width="250" height="314"/>
                                                    </imageView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="83R-2S-SDx">
                                                        <rect key="frame" x="16.5" y="330" width="304" height="96"/>
                                                        <subviews>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="W4x-zG-GCy">
                                                                <rect key="frame" x="0.0" y="0.0" width="304" height="44"/>
                                                                <color key="backgroundColor" name="Primary"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="GUo-iE-9cH"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                                                <state key="normal" title="I have connected">
                                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="connectedButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="09e-K7-T6r"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rYL-lf-5Ec">
                                                                <rect key="frame" x="0.0" y="52" width="304" height="44"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="44" id="4J8-9B-hi9"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                                                                <state key="normal" title="Cancel">
                                                                    <color key="titleColor" name="Primary"/>
                                                                </state>
                                                                <connections>
                                                                    <action selector="cancelConnectedButtonTapped:" destination="Y6W-OH-hqX" eventType="touchUpInside" id="OFe-bs-bfN"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="83R-2S-SDx" firstAttribute="width" secondItem="SiI-IS-77L" secondAttribute="width" multiplier="0.9" id="RrO-ki-ZsV"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="SiI-IS-77L" firstAttribute="top" secondItem="YxW-j1-7Q7" secondAttribute="bottom" constant="30" id="BG4-VL-wpq"/>
                                            <constraint firstAttribute="bottom" secondItem="SiI-IS-77L" secondAttribute="bottom" constant="30" id="DPg-fr-nNT"/>
                                            <constraint firstItem="YxW-j1-7Q7" firstAttribute="centerX" secondItem="eRZ-Ct-N2q" secondAttribute="centerX" id="Ed8-Up-GJf"/>
                                            <constraint firstItem="SiI-IS-77L" firstAttribute="centerX" secondItem="eRZ-Ct-N2q" secondAttribute="centerX" id="MW2-GI-IDM"/>
                                            <constraint firstItem="8UL-Ca-KTq" firstAttribute="top" secondItem="eRZ-Ct-N2q" secondAttribute="top" constant="8" id="QT2-tk-vFG"/>
                                            <constraint firstItem="YxW-j1-7Q7" firstAttribute="top" secondItem="eRZ-Ct-N2q" secondAttribute="top" constant="30" id="Svq-jR-POF"/>
                                            <constraint firstItem="SiI-IS-77L" firstAttribute="width" secondItem="eRZ-Ct-N2q" secondAttribute="width" multiplier="0.9" id="VFk-Ju-b5h"/>
                                            <constraint firstAttribute="trailing" secondItem="8UL-Ca-KTq" secondAttribute="trailing" constant="8" id="eAX-p3-VVY"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.69999999999999996" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="eRZ-Ct-N2q" firstAttribute="width" secondItem="CeV-SO-fEM" secondAttribute="width" id="D03-mw-xns"/>
                                    <constraint firstAttribute="bottom" secondItem="eRZ-Ct-N2q" secondAttribute="bottom" id="XQ2-ah-EQU"/>
                                    <constraint firstItem="eRZ-Ct-N2q" firstAttribute="height" secondItem="CeV-SO-fEM" secondAttribute="height" multiplier="0.85" id="aLZ-qe-bmi"/>
                                    <constraint firstItem="eRZ-Ct-N2q" firstAttribute="centerX" secondItem="CeV-SO-fEM" secondAttribute="centerX" id="jxu-po-p0k"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Xwd-Qd-8Zi" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" constant="8" id="1fZ-oM-ZHm"/>
                            <constraint firstItem="CeV-SO-fEM" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="4DU-WP-mWO"/>
                            <constraint firstItem="fsi-O6-WzE" firstAttribute="width" secondItem="3SM-bP-exd" secondAttribute="width" id="5lc-Yc-GGd"/>
                            <constraint firstItem="Sjm-cj-hYg" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="7AN-6F-APG"/>
                            <constraint firstItem="fsi-O6-WzE" firstAttribute="centerY" secondItem="3SM-bP-exd" secondAttribute="centerY" id="7bU-ZT-AVh"/>
                            <constraint firstItem="Q6t-gd-mAQ" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="9W5-Ie-15v"/>
                            <constraint firstItem="0iD-HJ-nBq" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="ABf-a0-2Bd"/>
                            <constraint firstItem="Sjm-cj-hYg" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="CNT-b6-M3R"/>
                            <constraint firstItem="3SM-bP-exd" firstAttribute="leading" secondItem="Xwd-Qd-8Zi" secondAttribute="trailing" constant="12" id="IEi-1d-LzB"/>
                            <constraint firstItem="CeV-SO-fEM" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="JPh-zF-O4C"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="0iD-HJ-nBq" secondAttribute="bottom" id="RJ6-Cp-HRB"/>
                            <constraint firstItem="CeV-SO-fEM" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="Rn3-Al-qcj"/>
                            <constraint firstItem="Xwd-Qd-8Zi" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" constant="8" id="Sog-Qg-Xcw"/>
                            <constraint firstItem="Q6t-gd-mAQ" firstAttribute="bottom" secondItem="vDu-zF-Fre" secondAttribute="bottom" id="Sum-XO-NtW"/>
                            <constraint firstItem="Sjm-cj-hYg" firstAttribute="trailing" secondItem="vDu-zF-Fre" secondAttribute="trailing" id="ZOs-8h-FN5"/>
                            <constraint firstItem="Sjm-cj-hYg" firstAttribute="bottom" secondItem="vDu-zF-Fre" secondAttribute="bottom" id="aDr-nO-wFR"/>
                            <constraint firstItem="zTk-xP-luk" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.92" id="cx2-80-1UW"/>
                            <constraint firstItem="zTk-xP-luk" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="edU-pA-G04"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="bottom" secondItem="mfZ-Ih-Yhj" secondAttribute="bottom" constant="16" id="hVV-GR-brH"/>
                            <constraint firstItem="0iD-HJ-nBq" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="j5g-1e-fWP"/>
                            <constraint firstItem="zTk-xP-luk" firstAttribute="centerY" secondItem="5EZ-qb-Rvc" secondAttribute="centerY" id="jDL-Z8-IE3"/>
                            <constraint firstItem="fsi-O6-WzE" firstAttribute="height" secondItem="3SM-bP-exd" secondAttribute="height" id="oeG-7X-LWI"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="3SM-bP-exd" secondAttribute="trailing" constant="12" id="pLY-yY-dzc"/>
                            <constraint firstItem="Q6t-gd-mAQ" firstAttribute="leading" secondItem="vDu-zF-Fre" secondAttribute="leading" id="qao-63-BUq"/>
                            <constraint firstItem="Q6t-gd-mAQ" firstAttribute="top" secondItem="vDu-zF-Fre" secondAttribute="top" id="qew-1c-Zdk"/>
                            <constraint firstItem="fsi-O6-WzE" firstAttribute="centerX" secondItem="3SM-bP-exd" secondAttribute="centerX" id="qqM-Wc-20S"/>
                            <constraint firstItem="mfZ-Ih-Yhj" firstAttribute="centerX" secondItem="5EZ-qb-Rvc" secondAttribute="centerX" id="rXl-U9-GOA"/>
                            <constraint firstItem="Fe7-nq-gtC" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.35" id="u9G-Cn-Kly"/>
                            <constraint firstItem="CeV-SO-fEM" firstAttribute="bottom" secondItem="vDu-zF-Fre" secondAttribute="bottom" id="vZa-7u-iMd"/>
                            <constraint firstItem="3SM-bP-exd" firstAttribute="centerY" secondItem="Xwd-Qd-8Zi" secondAttribute="centerY" id="vg5-CF-ovv"/>
                            <constraint firstItem="vDu-zF-Fre" firstAttribute="trailing" secondItem="0iD-HJ-nBq" secondAttribute="trailing" id="w83-8p-U4M"/>
                            <constraint firstItem="mfZ-Ih-Yhj" firstAttribute="width" secondItem="vDu-zF-Fre" secondAttribute="width" multiplier="0.9" id="ycT-2d-CGh"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="availableBalanceLabel" destination="Tcf-53-8Ts" id="lKm-Ix-TLS"/>
                        <outlet property="btnCancelTransactionPop" destination="WKr-bO-A8d" id="Of8-89-onf"/>
                        <outlet property="btnSkipRatings" destination="knq-GK-E26" id="Zeq-sZ-D3Y"/>
                        <outlet property="btnVehicle" destination="fsi-O6-WzE" id="udR-VX-fJq"/>
                        <outlet property="cancelConnectedButton" destination="rYL-lf-5Ec" id="1Vz-Pv-XxR"/>
                        <outlet property="carView" destination="zHn-89-Fdq" id="q6z-z7-c0H"/>
                        <outlet property="chargePointLabel" destination="2Pr-qQ-YKc" id="XUi-BA-58i"/>
                        <outlet property="chargingButton" destination="mfZ-Ih-Yhj" id="b1t-tK-veL"/>
                        <outlet property="chargingDetailsView" destination="wCn-sc-w3v" id="50K-3e-t2z"/>
                        <outlet property="connectedButton" destination="W4x-zG-GCy" id="uGS-vP-rjp"/>
                        <outlet property="consumedUnitsLabel" destination="mGJ-OX-H8p" id="uSa-j3-FaT"/>
                        <outlet property="cosmosRatings" destination="kgH-ad-hK3" id="nUq-vB-NQ8"/>
                        <outlet property="datePickerChargeType" destination="NNh-jR-CIk" id="13x-KL-c71"/>
                        <outlet property="kwhLabel" destination="s0v-qr-wpf" id="rWB-jI-edb"/>
                        <outlet property="lblBalance" destination="fWB-Wc-jiT" id="Ujt-TJ-hW1"/>
                        <outlet property="lblEstimatedPrice" destination="3Hb-Eq-0qR" id="xw2-Aa-Wf3"/>
                        <outlet property="lblInitial" destination="Z9v-Kq-8l6" id="3bd-Mh-9xl"/>
                        <outlet property="lblUserName" destination="C70-uh-AjW" id="nAI-9C-TSp"/>
                        <outlet property="lblVehicle" destination="KUR-b0-aEG" id="4mo-bg-8f3"/>
                        <outlet property="menuButton" destination="Xwd-Qd-8Zi" id="fhB-ri-Bch"/>
                        <outlet property="percentLabel" destination="Fha-vO-IlX" id="U4x-TB-wKX"/>
                        <outlet property="ratingChargeStationNameLabel" destination="MWx-1O-LaE" id="IOh-YM-Xvc"/>
                        <outlet property="ratingsButton" destination="tMK-hJ-AwD" id="bQ2-dm-WWX"/>
                        <outlet property="ratingsMainView" destination="mwf-F3-6mD" id="iKx-5L-2d6"/>
                        <outlet property="ratingsTopView" destination="QTm-mV-a9C" id="Z93-QV-ovH"/>
                        <outlet property="scanQRButton" destination="6je-cS-yf2" id="cwY-Df-bUw"/>
                        <outlet property="stackNotCharging" destination="GTI-JN-EWT" id="8l9-qH-ycZ"/>
                        <outlet property="startTimeLabel" destination="YtF-oT-272" id="tjE-mG-DWW"/>
                        <outlet property="tableHeight" destination="30W-2y-8Jh" id="EFO-hZ-bUe"/>
                        <outlet property="tableMenu" destination="Y2V-QK-ocX" id="xAM-tS-4NF"/>
                        <outlet property="timeDurationLabel" destination="6rS-xC-mfL" id="fGF-fV-5bL"/>
                        <outlet property="timeStartedLabel" destination="Mqv-3u-0Ij" id="uia-7d-VZ8"/>
                        <outlet property="totalChargesLabel" destination="z1Z-om-Wb8" id="ZSn-Sh-qsZ"/>
                        <outlet property="totalChargesTotal" destination="iFx-CS-gyo" id="iTL-aj-OUe"/>
                        <outlet property="transactionBGView" destination="0iD-HJ-nBq" id="Fvn-uM-XQ6"/>
                        <outlet property="transactionImgView" destination="TYo-uJ-wXc" id="GH0-LR-pXH"/>
                        <outlet property="transactionMainView" destination="hk0-Ou-lGW" id="hsi-kt-QaH"/>
                        <outlet property="transactionMsgLabel" destination="vsg-k0-rrS" id="snG-HX-MhO"/>
                        <outlet property="transactionTitleLabel" destination="S3i-jN-Jkz" id="du1-rb-rmt"/>
                        <outlet property="transactionTop" destination="bqf-Bu-iWE" id="W99-3B-OwH"/>
                        <outlet property="txtUnitChargeType" destination="KEn-3I-7jE" id="D8f-sb-C23"/>
                        <outlet property="viewBgChargeType" destination="Sjm-cj-hYg" id="nVS-NJ-yoD"/>
                        <outlet property="viewBgMenu" destination="Q6t-gd-mAQ" id="3v4-jj-1KF"/>
                        <outlet property="viewBgNDCP" destination="CeV-SO-fEM" id="gMr-Cl-p44"/>
                        <outlet property="viewFullCharged" destination="idf-9n-b8k" id="Chx-cF-cJa"/>
                        <outlet property="viewMainChargeType" destination="1uA-g0-h6Z" id="cDu-hM-pdY"/>
                        <outlet property="viewMainMenu" destination="mwp-P2-Cxg" id="5gr-tU-19F"/>
                        <outlet property="viewMainNDCP" destination="eRZ-Ct-N2q" id="VYG-4d-cjR"/>
                        <outlet property="viewProfile" destination="F6g-RN-oUl" id="YWA-iJ-M62"/>
                        <outlet property="viewTime" destination="hxQ-5D-7cW" id="0hX-CY-ITr"/>
                        <outlet property="viewTransactionButton" destination="l61-qb-pgg" id="rIM-do-saD"/>
                        <outlet property="viewUnit" destination="mHn-gQ-4Io" id="50a-Eh-2z3"/>
                        <outlet property="viewVehicleSelection" destination="3SM-bP-exd" id="f0f-9z-FVO"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-20" y="45"/>
        </scene>
        <!--ScanQRVC-->
        <scene sceneID="04e-uC-TsX">
            <objects>
                <viewController storyboardIdentifier="ScanQRVC" id="m9k-cT-9hC" customClass="ScanQRVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Th0-bh-yya">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jNK-l4-BkZ">
                                <rect key="frame" x="0.0" y="20" width="375" height="587"/>
                                <color key="backgroundColor" white="0.0" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hlK-PH-ycN">
                                <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uGZ-zK-lsW">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="2ZK-JJ-XYq"/>
                                            <constraint firstAttribute="width" constant="34" id="aoZ-pg-CWp"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backAction:" destination="m9k-cT-9hC" eventType="touchUpInside" id="6xQ-5A-nMm"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Scan QR" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="d3w-q1-yzJ">
                                        <rect key="frame" x="153.5" y="12" width="68" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="d3w-q1-yzJ" firstAttribute="centerY" secondItem="hlK-PH-ycN" secondAttribute="centerY" id="9do-L0-oM5"/>
                                    <constraint firstItem="d3w-q1-yzJ" firstAttribute="centerX" secondItem="hlK-PH-ycN" secondAttribute="centerX" id="CWa-6e-4wf"/>
                                    <constraint firstItem="uGZ-zK-lsW" firstAttribute="leading" secondItem="hlK-PH-ycN" secondAttribute="leading" constant="12" id="b0I-k2-TPh"/>
                                    <constraint firstAttribute="height" constant="44" id="hUa-L5-leg"/>
                                    <constraint firstItem="uGZ-zK-lsW" firstAttribute="centerY" secondItem="hlK-PH-ycN" secondAttribute="centerY" id="pla-R2-Kjt"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UaI-TL-LQK">
                                <rect key="frame" x="0.0" y="64" width="375" height="543"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="40K-vY-5MA">
                                <rect key="frame" x="162.5" y="453" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="kco-3K-Ud8"/>
                                    <constraint firstAttribute="width" constant="50" id="qRg-Mp-0IJ"/>
                                </constraints>
                                <color key="tintColor" white="1" alpha="0.80000000000000004" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="ic_flash">
                                    <color key="titleColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <state key="selected" image="ic_flash">
                                    <color key="titleColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="flashButtonTapped:" destination="m9k-cT-9hC" eventType="touchUpInside" id="8GG-ce-w54"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xq2-e4-tJO">
                                <rect key="frame" x="37.5" y="533" width="300" height="44"/>
                                <color key="backgroundColor" white="1" alpha="0.90000000000000002" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="Quq-EM-1zs"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" title="Cancel">
                                    <color key="titleColor" name="Primary"/>
                                </state>
                                <connections>
                                    <action selector="cancelButtonTapped:" destination="m9k-cT-9hC" eventType="touchUpInside" id="C9e-Lj-yn3"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ggw-8n-pFj">
                                <rect key="frame" x="0.0" y="607" width="375" height="60"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_scan" translatesAutoresizingMaskIntoConstraints="NO" id="V7Q-qr-NCe">
                                        <rect key="frame" x="12" y="6" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="V7Q-qr-NCe" secondAttribute="height" multiplier="1:1" id="unx-Mj-JY0"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Scan the QR Code" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oZE-4B-COd">
                                        <rect key="frame" x="72" y="6" width="191" height="18"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                        <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mEn-Ry-8dF">
                                        <rect key="frame" x="275" y="6" width="88" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="88" id="8Qr-Nh-fhG"/>
                                            <constraint firstAttribute="height" constant="48" id="Kcg-Ub-Aqr"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" title="Manual Entry">
                                            <color key="titleColor" name="Primary"/>
                                        </state>
                                        <connections>
                                            <action selector="manualEntryButtonTapped:" destination="m9k-cT-9hC" eventType="touchUpInside" id="Xzf-Ey-Qhd"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="or" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qhf-Ey-8dF">
                                        <rect key="frame" x="72" y="30" width="191" height="18"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                        <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="oZE-4B-COd" firstAttribute="top" secondItem="ggw-8n-pFj" secondAttribute="top" constant="6" id="2Qr-Nh-fhG"/>
                                    <constraint firstAttribute="height" constant="60" id="V1Q-Y1-qQ8"/>
                                    <constraint firstItem="mEn-Ry-8dF" firstAttribute="trailing" secondItem="ggw-8n-pFj" secondAttribute="trailing" constant="-12" id="Zs0-PN-GBb"/>
                                    <constraint firstAttribute="bottom" secondItem="V7Q-qr-NCe" secondAttribute="bottom" constant="6" id="aXg-gW-rxU"/>
                                    <constraint firstItem="oZE-4B-COd" firstAttribute="leading" secondItem="V7Q-qr-NCe" secondAttribute="trailing" constant="12" id="axv-ta-q5v"/>
                                    <constraint firstItem="V7Q-qr-NCe" firstAttribute="leading" secondItem="ggw-8n-pFj" secondAttribute="leading" constant="12" id="tAQ-6h-azu"/>
                                    <constraint firstItem="V7Q-qr-NCe" firstAttribute="top" secondItem="ggw-8n-pFj" secondAttribute="top" constant="6" id="xxg-rW-fZN"/>
                                    <constraint firstItem="mEn-Ry-8dF" firstAttribute="leading" secondItem="oZE-4B-COd" secondAttribute="trailing" constant="12" id="mEn-Ry-8dF-leading"/>
                                    <constraint firstItem="mEn-Ry-8dF" firstAttribute="top" secondItem="ggw-8n-pFj" secondAttribute="top" constant="6" id="mEn-Ry-8dF-top"/>
                                    <constraint firstItem="Qhf-Ey-8dF" firstAttribute="leading" secondItem="V7Q-qr-NCe" secondAttribute="trailing" constant="12" id="Qhf-Ey-8dF-leading"/>
                                    <constraint firstItem="Qhf-Ey-8dF" firstAttribute="trailing" secondItem="oZE-4B-COd" secondAttribute="trailing" id="Qhf-Ey-8dF-trailing"/>
                                    <constraint firstItem="Qhf-Ey-8dF" firstAttribute="top" secondItem="oZE-4B-COd" secondAttribute="bottom" constant="6" id="Qhf-Ey-8dF-top"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7hs-9n-Rs0"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="ggw-8n-pFj" firstAttribute="top" secondItem="jNK-l4-BkZ" secondAttribute="bottom" id="2jL-AX-5XY"/>
                            <constraint firstItem="7hs-9n-Rs0" firstAttribute="trailing" secondItem="ggw-8n-pFj" secondAttribute="trailing" id="4Z8-NQ-Eky"/>
                            <constraint firstItem="jNK-l4-BkZ" firstAttribute="top" secondItem="7hs-9n-Rs0" secondAttribute="top" id="5fr-l6-Dez"/>
                            <constraint firstItem="xq2-e4-tJO" firstAttribute="centerX" secondItem="Th0-bh-yya" secondAttribute="centerX" id="Aft-GL-94v"/>
                            <constraint firstItem="hlK-PH-ycN" firstAttribute="leading" secondItem="7hs-9n-Rs0" secondAttribute="leading" id="FJa-fi-pN1"/>
                            <constraint firstItem="hlK-PH-ycN" firstAttribute="trailing" secondItem="7hs-9n-Rs0" secondAttribute="trailing" id="Je1-1O-gND"/>
                            <constraint firstItem="ggw-8n-pFj" firstAttribute="top" secondItem="xq2-e4-tJO" secondAttribute="bottom" constant="30" id="MAr-UP-Yvu"/>
                            <constraint firstItem="7hs-9n-Rs0" firstAttribute="bottom" secondItem="ggw-8n-pFj" secondAttribute="bottom" id="OPv-bP-op6"/>
                            <constraint firstItem="jNK-l4-BkZ" firstAttribute="leading" secondItem="7hs-9n-Rs0" secondAttribute="leading" id="ZCk-zf-qUZ"/>
                            <constraint firstItem="40K-vY-5MA" firstAttribute="centerX" secondItem="Th0-bh-yya" secondAttribute="centerX" id="cft-H4-bdu"/>
                            <constraint firstItem="ggw-8n-pFj" firstAttribute="top" secondItem="UaI-TL-LQK" secondAttribute="bottom" id="dka-NT-JaW"/>
                            <constraint firstItem="ggw-8n-pFj" firstAttribute="leading" secondItem="7hs-9n-Rs0" secondAttribute="leading" id="g8D-Mu-vyp"/>
                            <constraint firstItem="7hs-9n-Rs0" firstAttribute="trailing" secondItem="UaI-TL-LQK" secondAttribute="trailing" id="hpK-h9-wwP"/>
                            <constraint firstItem="xq2-e4-tJO" firstAttribute="width" secondItem="7hs-9n-Rs0" secondAttribute="width" multiplier="0.8" id="iH9-pE-LDp"/>
                            <constraint firstItem="xq2-e4-tJO" firstAttribute="top" secondItem="40K-vY-5MA" secondAttribute="bottom" constant="30" id="ilR-Ru-WiW"/>
                            <constraint firstItem="7hs-9n-Rs0" firstAttribute="trailing" secondItem="jNK-l4-BkZ" secondAttribute="trailing" id="p26-9b-m6b"/>
                            <constraint firstItem="UaI-TL-LQK" firstAttribute="top" secondItem="hlK-PH-ycN" secondAttribute="bottom" id="pVr-Ip-HsO"/>
                            <constraint firstItem="UaI-TL-LQK" firstAttribute="leading" secondItem="7hs-9n-Rs0" secondAttribute="leading" id="zoC-AR-a0S"/>
                            <constraint firstItem="hlK-PH-ycN" firstAttribute="top" secondItem="7hs-9n-Rs0" secondAttribute="top" id="zrK-dz-0S0"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backButton" destination="uGZ-zK-lsW" id="8h6-MB-ocz"/>
                        <outlet property="cancelButton" destination="xq2-e4-tJO" id="Bsd-xO-UTT"/>
                        <outlet property="flastButton" destination="40K-vY-5MA" id="syc-E5-fHq"/>
                        <outlet property="manualEntryButton" destination="mEn-Ry-8dF" id="mEn-Ry-8dF-outlet"/>
                        <outlet property="viewMain" destination="UaI-TL-LQK" id="FRH-4G-ObZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8tt-Yv-xqd" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="693.60000000000002" y="44.527736131934034"/>
        </scene>
        <!--Connection LostVC-->
        <scene sceneID="TjD-Fr-UIo">
            <objects>
                <viewController storyboardIdentifier="ConnectionLostVC" id="gfB-eJ-Ht9" customClass="ConnectionLostVC" customModule="NXC_EV_Solutions" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="z2M-gJ-E2p">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uau-eK-0W4">
                                <rect key="frame" x="0.0" y="20" width="375" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nEb-Rg-Hm0">
                                        <rect key="frame" x="12" y="5" width="34" height="34"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="34" id="S8R-tg-sQc"/>
                                            <constraint firstAttribute="width" constant="34" id="eze-Dd-D1h"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="8" minY="6" maxX="8" maxY="6"/>
                                        <state key="normal" image="ic_back_arrow"/>
                                        <connections>
                                            <action selector="backActionTapped:" destination="gfB-eJ-Ht9" eventType="touchUpInside" id="8ob-Dr-CO4"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Connection Lost" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m53-ig-Y0f">
                                        <rect key="frame" x="122.5" y="12" width="130" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                        <color key="textColor" red="0.070588235289999995" green="0.070588235289999995" blue="0.070588235289999995" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="nEb-Rg-Hm0" firstAttribute="centerY" secondItem="uau-eK-0W4" secondAttribute="centerY" id="B2u-tN-TuA"/>
                                    <constraint firstAttribute="height" constant="44" id="P4P-x7-HkP"/>
                                    <constraint firstItem="nEb-Rg-Hm0" firstAttribute="leading" secondItem="uau-eK-0W4" secondAttribute="leading" constant="12" id="S4H-56-TUI"/>
                                    <constraint firstItem="m53-ig-Y0f" firstAttribute="centerX" secondItem="uau-eK-0W4" secondAttribute="centerX" id="Tje-PQ-bwm"/>
                                    <constraint firstItem="m53-ig-Y0f" firstAttribute="centerY" secondItem="uau-eK-0W4" secondAttribute="centerY" id="Zwp-lH-XsU"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_connection Lost (1)" translatesAutoresizingMaskIntoConstraints="NO" id="lBe-s7-MEG">
                                <rect key="frame" x="37.5" y="117" width="300" height="300"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="300" id="CbX-It-Ov9"/>
                                    <constraint firstAttribute="width" constant="300" id="P1d-4v-yrl"/>
                                </constraints>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Cvf-BL-PfG">
                                <rect key="frame" x="28" y="599" width="319" height="44"/>
                                <color key="backgroundColor" name="Primary"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="Q4I-gz-AAv"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="17"/>
                                <state key="normal" title="Try Again">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="tryAgainTapped:" destination="gfB-eJ-Ht9" eventType="touchUpInside" id="x4a-JR-pi9"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Could not connect to the server" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SB2-I7-ejg">
                                <rect key="frame" x="28" y="441" width="319" height="18"/>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please check your internet connection" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hpr-6h-86h">
                                <rect key="frame" x="56.5" y="463" width="262" height="18"/>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oLX-YX-8UT"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="SB2-I7-ejg" firstAttribute="top" secondItem="lBe-s7-MEG" secondAttribute="bottom" constant="24" id="0Ub-8l-716"/>
                            <constraint firstItem="oLX-YX-8UT" firstAttribute="bottom" secondItem="Cvf-BL-PfG" secondAttribute="bottom" constant="24" id="J4a-YC-pH1"/>
                            <constraint firstItem="hpr-6h-86h" firstAttribute="centerX" secondItem="z2M-gJ-E2p" secondAttribute="centerX" id="JCY-pK-z3b"/>
                            <constraint firstItem="Cvf-BL-PfG" firstAttribute="centerX" secondItem="z2M-gJ-E2p" secondAttribute="centerX" id="N23-jx-OXa"/>
                            <constraint firstItem="uau-eK-0W4" firstAttribute="trailing" secondItem="oLX-YX-8UT" secondAttribute="trailing" id="RW1-4u-Zdg"/>
                            <constraint firstItem="Cvf-BL-PfG" firstAttribute="width" secondItem="oLX-YX-8UT" secondAttribute="width" multiplier="0.85" id="Rye-fC-cc1"/>
                            <constraint firstItem="lBe-s7-MEG" firstAttribute="centerX" secondItem="z2M-gJ-E2p" secondAttribute="centerX" id="XVf-Ge-SdA"/>
                            <constraint firstItem="SB2-I7-ejg" firstAttribute="centerX" secondItem="z2M-gJ-E2p" secondAttribute="centerX" id="dC8-Q1-r4G"/>
                            <constraint firstItem="hpr-6h-86h" firstAttribute="top" secondItem="SB2-I7-ejg" secondAttribute="bottom" constant="4" id="eXV-2t-RI5"/>
                            <constraint firstItem="SB2-I7-ejg" firstAttribute="width" secondItem="oLX-YX-8UT" secondAttribute="width" multiplier="0.85" id="fRo-rP-pYR"/>
                            <constraint firstItem="lBe-s7-MEG" firstAttribute="centerY" secondItem="z2M-gJ-E2p" secondAttribute="centerY" multiplier="0.8" id="lwr-WF-1FM"/>
                            <constraint firstItem="uau-eK-0W4" firstAttribute="top" secondItem="oLX-YX-8UT" secondAttribute="top" id="td1-V5-enq"/>
                            <constraint firstItem="uau-eK-0W4" firstAttribute="leading" secondItem="oLX-YX-8UT" secondAttribute="leading" id="vW2-Or-gFl"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnBack" destination="nEb-Rg-Hm0" id="18v-bY-2Rd"/>
                        <outlet property="btnTryAgain" destination="Cvf-BL-PfG" id="hHL-xW-lCW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="VFb-5M-nGi" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1359" y="45"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_animate_bg" width="580" height="580"/>
        <image name="ic_back_arrow" width="64" height="64"/>
        <image name="ic_cancel" width="64" height="64"/>
        <image name="ic_car_wallet" width="500" height="316"/>
        <image name="ic_charge" width="500" height="500"/>
        <image name="ic_connection Lost (1)" width="300" height="300"/>
        <image name="ic_down_arrow" width="209" height="105"/>
        <image name="ic_flash" width="100" height="100"/>
        <image name="ic_full_charge" width="30" height="30"/>
        <image name="ic_logout" width="24" height="24"/>
        <image name="ic_menu" width="500" height="500"/>
        <image name="ic_plug_connected" width="250" height="250"/>
        <image name="ic_price" width="30" height="30"/>
        <image name="ic_ratings-1" width="300" height="300"/>
        <image name="ic_scan" width="400" height="534"/>
        <image name="ic_transaction_ successful" width="300" height="300"/>
        <image name="ic_unite" width="30" height="30"/>
        <namedColor name="Primary">
            <color red="0.016000000759959221" green="0.49399998784065247" blue="0.42699998617172241" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimarySelection">
            <color red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="PrimaryTextLight">
            <color red="0.28627450980392155" green="0.28627450980392155" blue="0.28627450980392155" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="UserProfileBG">
            <color red="0.97647058823529409" green="0.97647058823529409" blue="0.97647058823529409" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="opaqueSeparatorColor">
            <color red="0.77647058823529413" green="0.77647058823529413" blue="0.78431372549019607" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemYellowColor">
            <color red="1" green="0.80000000000000004" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
