////
////  ScanQRVC.swift
////  ScanQRVC
////
////  Created by <PERSON><PERSON><PERSON> on 17/09/21.
////
//
//import UIKit
//import MercariQRScanner
//import SwiftQRScanner
//
//class ScanQRVC: UIViewController {
//
//    // MARK: - IBOutlets
////    @IBOutlet weak var scannerView: QRScannerView!
////    @IBOutlet weak var flastButton: UIButton!
////    @IBOutlet weak var cancelButton: UIButton!
//
//    @IBOutlet weak var viewMain: UIView!
//    @IBOutlet weak var backButton: UIButton!
//
//    // MARK: - View LifeCycle Methods
//    override func viewDidLoad() {
//        super.viewDidLoad()
//
////        let scanner = QRCodeScannerController()
////        scanner.delegate = self
////        self.present(scanner, animated: true, completion: nil)
//
////        let scanner = QRCodeScannerController(cameraImage: UIImage(named: "camera"), cancelImage: UIImage(named: "cancel"), flashOnImage: UIImage(named: "flash-on"), flashOffImage: UIImage(named: "flash-off"))
////        scanner.delegate = self
////        self.present(scanner, animated: true, completion: nil)
//
////        scannerView.configure(delegate: self)
//
////        let blurEffect = UIBlurEffect(style: .dark)
////        scannerView = UIVisualEffectView(effect: blurEffect)
////        scannerView.frame = self.view.bounds
////        scannerView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//
////        view.addSubview(blurEffectView)
//
//
////        scannerView.configure(delegate: self, input: .init(isBlurEffectEnabled: true))
////        scannerView.startRunning()
//
//    }
//
//    override func viewDidLayoutSubviews() {
//
////        cancelButton.shadowWithCRadius(radius: btnMenu.frame.height/2, color: Constants.secondaryGrayText!)
////        viewBgDetails.shadowWithCRadius(radius: 24, color: Constants.secondaryGrayText!)
////        scannerView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
////        flastButton.shadowWithCRadius(radius: flastButton.frame.height/2, color: Constants.secondaryGrayText!)
////        cancelButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
//    }
//
//    // MARK: - Button Actions
//    @IBAction func flashButtonTapped(_ sender: UIButton) {
////        scannerView.setTorchActive(isOn: !sender.isSelected)
//    }
//
//    @IBAction func cancelButtonTapped(_ sender: UIButton) {
//        self.navigationController?.popViewController(animated: true)
//    }
//
//    @IBAction func backButtonTapped(_ sender: UIButton) {
//        navigationController?.popViewController(animated: true)
//    }
//
//}
////extension ScanQRVC: QRScannerViewDelegate {
////    func qrScannerView(_ qrScannerView: QRScannerView, didFailure error: QRScannerError) {
////        print("error:\(error)")
////    }
////
////    func qrScannerView(_ qrScannerView: QRScannerView, didSuccess code: String) {
////        print("code:\(code)")
////    }
////
////    func qrScannerView(_ qrScannerView: QRScannerView, didChangeTorchActive isOn: Bool) {
////        flastButton.isSelected = isOn
////        if flastButton.isSelected == true {
////            flastButton.setImage(UIImage(named: "ic_flash"), for: .normal)
//////            flastButton.backgroundColor = UIColor.white.withAlphaComponent(0.9)
////        } else {
////            flastButton.setImage(UIImage(named: "ic_flash"), for: .normal)
//////            flastButton.backgroundColor = UIColor.white.withAlphaComponent(0.9)
////        }
////    }
////
//////    func qrScannerView(_ qrScannerView: QRScannerView, didChangeTorchActive isOn: Bool) {
//////            flashButton.isSelected = isOn
//////        }
////}
//extension ScanQRVC: QRScannerCodeDelegate {
//    func qrCodeScanningDidCompleteWithResult(result: String) {
//        print("result1:\(result)")
//    }
//
//    func qrCodeScanningFailedWithError(error: String) {
//        print("error1:\(error)")
//    }
//
//    func qrScanner(_ controller: UIViewController, scanDidComplete result: String) {
//        print("result2:\(result)")
//    }
//
//    func qrScannerDidFail(_ controller: UIViewController, error: String) {
//        print("error2:\(error)")
//    }
//
//    func qrScannerDidCancel(_ controller: UIViewController) {
//        print("SwiftQRScanner did cancel")
//    }
//}


//
//  BarcodeScanVC.swift
//  mybooks
//
//  Created by devloper on 29/05/19.
//  Copyright © 2019 nxcControls. All rights reserved.
//


import Foundation
import UIKit
import AVFoundation
import Firebase
import FirebaseAuth
import ACFloatingTextfield_Swift
//import SnapKit
import SwiftQRScanner
//import swiftScan
import Alamofire
import Starscream


class ScanQRVC: UIViewController,AVCaptureVideoDataOutputSampleBufferDelegate,AVCaptureMetadataOutputObjectsDelegate,LBXScanViewControllerDelegate
{

    func scanFinished(scanResult: LBXScanResult, error: String?) {
        print(scanResult)
        print(error!)
    }


    // MARK: - All IBOutlets
    @IBOutlet weak var viewMain: UIView!
    @IBOutlet weak var backButton: UIButton!
    @IBOutlet weak var flastButton: UIButton!
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var manualEntryButton: UIButton!


    var param:[String:Any] = [:]
    var anotherToken:String = String()
    var RToken:String = String()
    var RPhone:String = String()
    var RName:String = String()

    var qRScanView: LBXScanView?
    open weak var scanResultDelegate: LBXScanViewControllerDelegate?
    open var delegate: QRRectDelegate?
    open var scanObj: LBXScanWrapper?
    open var scanStyle: LBXScanViewStyle? = LBXScanViewStyle()

    open var isOpenInterestRect = false
    public var arrayCodeType:[AVMetadataObject.ObjectType]?
    public  var isNeedCodeImage = false
    public var readyString:String! = "loading"

    var searchActive : Bool = false
    var strTableTap:String = String()
    var socket: WebSocket!

    //8991857040027879095
    //32767004C4E213E1D1D40D84D3AEC1b9


    // MARK: - View LifeCycle
    override func viewDidLoad() {


    }

    override func viewWillAppear(_ animated: Bool) {
        cameraSelected()
        drawScanView()
        perform(#selector(LBXScanViewController.startScan), with: nil, afterDelay: 0.3)

    }

    @objc open func startScan() {

        if (scanObj == nil) {
            var cropRect = CGRect.zero
            if isOpenInterestRect {
                cropRect = LBXScanView.getScanRectWithPreView(preView: self.viewMain, style:scanStyle! )
            }


            //指定识别几种码
            if arrayCodeType == nil {
                arrayCodeType = [AVMetadataObject.ObjectType.qr as NSString ,AVMetadataObject.ObjectType.ean13 as NSString ,AVMetadataObject.ObjectType.code128 as NSString] as [AVMetadataObject.ObjectType]
            }

//            scanObj = LBXScanWrapper(video
            scanObj = LBXScanWrapper(videoPreView: self.viewMain,objType:arrayCodeType!, isCaptureImg: isNeedCodeImage,cropRect:cropRect, success: { [weak self] (arrayResult) -> Void in

                if let strongSelf = self {
                    //停止扫描动画
                    strongSelf.qRScanView?.stopScanAnimation()
                    strongSelf.handleCodeResult(arrayResult: arrayResult)
                }
            })
        }

        //结束相机等待提示
        qRScanView?.deviceStopReadying()

        //开始扫描动画
        qRScanView?.startScanAnimation()

        //相机运行
        scanObj?.start()
    }


    open func handleCodeResult(arrayResult:[LBXScanResult]) {

        if let delegate = scanResultDelegate  {

            self.navigationController?.popViewController(animated: true)
            let result:LBXScanResult = arrayResult[0]
            delegate.scanFinished(scanResult: result, error: nil)

        } else {

            for result:LBXScanResult in arrayResult {
                print("%@",result.strScanned ?? "")
            }

            let result:LBXScanResult = arrayResult[0]
            let str = result.strScanned!.replacingOccurrences(of: "tel:", with: "")
            print(str)

            UserDefaults.standard.set("\(str)", forKey:  Constants.SCAN_QRCODE)

            //print("done click")

            AppDelegate.shared.isFromScan = "1"

            let dic = [ "event":WebSocketDetails.EVENT_SCANQR,
                        "user_id":"\(UserDefaults.standard.object(forKey: Constants.USER_ID)!)",
                        "payload":"\(str)",
                        "trans_value":AppDelegate.shared.transValue,
                        "trans_type":AppDelegate.shared.transType,
                        "vehicle_id":AppDelegate.shared.strVehicleID]
            print(dic)
            let jsonData1 = try! JSONSerialization.data(withJSONObject: dic, options: [])
            print(jsonData1)
            let str1 = String(data: jsonData1, encoding: .utf8)
            print(str1!)

            if AppDelegate.shared.socket.isConnected {
                AppDelegate.shared.socket.write(string: str1!)
            }

            self.navigationController?.popViewController(animated: true)
        }
    }

    @objc func rightSwipe() {
        self.navigationController?.popViewController(animated: true)
    }

    func scanAnimation() {

        var style = LBXScanViewStyle()
        style.centerUpOffset = 135
        style.photoframeAngleStyle = LBXScanViewPhotoframeAngleStyle.Inner
        style.photoframeLineW = 3
        style.photoframeAngleW = 18
        style.photoframeAngleH = 18
        style.isNeedShowRetangle = false

        style.anmiationStyle = LBXScanViewAnimationStyle.LineMove

        //qq里面的线条图片
        style.animationImage = UIImage(named: "CodeScan.bundle/qrcode_scan_light_green")

        let vc = LBXScanViewController()
        vc.scanStyle = style
        vc.scanResultDelegate = self
        self.navigationController?.pushViewController(vc, animated: true)
    }

    open func drawScanView() {
        if qRScanView == nil {
            qRScanView = LBXScanView(frame: self.view.frame,vstyle:scanStyle! )
            self.viewMain.addSubview(qRScanView!)
            delegate?.drawwed()
        }
        qRScanView?.deviceStartReadying(readyStr: readyString)
    }

    override func viewDidLayoutSubviews() {
        cancelButton.shadowWithCRadius(radius: 12, color: Constants.secondaryGrayText!)
        backButton.addBorder(color: Constants.backArrowBorderColor!, width: Int(1))
        backButton.maskClipCorner(cornerRadius: 10)
    }

    // MARK: - Button Actions
    @objc func doneAction(_ sender : UITextField!) {
        self.view.endEditing(true)
        print(sender.text!)
    }

    @IBAction func manualEntryButtonTapped(_ sender: UIButton) {
        showManualEntryAlert()
    }

    @IBAction func backAction(_ sender: UIButton) {
        AppDelegate.shared.isFromOTPScan = "0"
        AppDelegate.shared.isFromStart = "0"
        self.navigationController?.popViewController(animated: true)
    }

    @IBAction func backButtonTapped(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }

    @IBAction func flashButtonTapped(_ sender: UIButton) {
        toggleFlash()
    }

    @IBAction func cancelButtonTapped(_ sender: UIButton) {
        self.navigationController?.popViewController(animated: true)
    }


    // MARK: - Flash Method
    func toggleFlash() {
        guard let device = AVCaptureDevice.default(for: AVMediaType.video) else { return }
        guard device.hasTorch else { return }

        do {
            try device.lockForConfiguration()

            if (device.torchMode == AVCaptureDevice.TorchMode.on) {
                device.torchMode = AVCaptureDevice.TorchMode.off
            } else {
                do {
                    try device.setTorchModeOn(level: 1.0)
                } catch {
                    print(error)
                }
            }

            device.unlockForConfiguration()
        } catch {
            print(error)
        }
    }

    // MARK: - Manual Entry Functions
    func showManualEntryAlert() {
        let alert = UIAlertController(title: "Manual Entry".localiz(), message: "Enter QR code manually".localiz(), preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "Enter QR Code".localiz()
            textField.autocapitalizationType = .none
            textField.autocorrectionType = .no
            textField.keyboardType = .default
        }

        let submitAction = UIAlertAction(title: "Submit".localiz(), style: .default) { [weak self] _ in
            guard let textField = alert.textFields?.first,
                  let qrCode = textField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
                  !qrCode.isEmpty else {
                self?.showAlert(title: "Invalid QR Code".localiz(), message: "Please enter QR code".localiz())
                return
            }

            if self?.validateQRCode(qrCode) == true {
                self?.processManualQRCode(qrCode)
            } else {
                self?.showAlert(title: "Invalid QR Code".localiz(), message: "Please enter a valid QR code".localiz())
            }
        }

        let cancelAction = UIAlertAction(title: "Cancel".localiz(), style: .cancel, handler: nil)

        alert.addAction(submitAction)
        alert.addAction(cancelAction)

        self.present(alert, animated: true, completion: nil)
    }

    func validateQRCode(_ qrCode: String) -> Bool {
        // Basic validation - check if QR code is not empty and has reasonable length
        let trimmedCode = qrCode.trimmingCharacters(in: .whitespacesAndNewlines)

        // QR codes should be at least 3 characters and not more than 2000 characters
        guard trimmedCode.count >= 3 && trimmedCode.count <= 2000 else {
            return false
        }

        // Additional validation can be added here based on expected QR code format
        // For now, we'll accept any non-empty string within reasonable length
        return true
    }

    func processManualQRCode(_ qrCode: String) {
        // Create a mock LBXScanResult for manual entry
        let manualResult = LBXScanResult(str: qrCode, img: nil, barCodeType: "Manual Entry", corner: nil)

        // Process the manually entered QR code the same way as scanned codes
        self.handleCodeResult(arrayResult: [manualResult])

        // Show confirmation message
        print("QR Code entered manually".localiz() + ": \(qrCode)")
    }

    func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
        self.present(alert, animated: true, completion: nil)
    }

    // MARK: - Convert to Dictionary
    func convertToDictionary(text: String) -> [String: Any]? {
        if let data = text.data(using: .utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
            } catch {
                print(error.localizedDescription)
            }
        }
        return nil
    }

    //MARK: - Webservice
    public func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [String : Any]) {
        picker.dismiss(animated: true, completion: nil)

        var image:UIImage? = info[UIImagePickerController.InfoKey.editedImage.rawValue] as? UIImage

        if (image == nil ) {
            image = info[UIImagePickerController.InfoKey.originalImage.rawValue] as? UIImage
        }

        if(image != nil) {
            let arrayResult = LBXScanWrapper.recognizeQRImage(image: image!)
            if arrayResult.count > 0 {
                handleCodeResult(arrayResult: arrayResult)
                return
            }
        }
        showMsg(title: nil, message: NSLocalizedString("Identify failed", comment: "Identify failed"))
    }

    func showMsg(title:String?,message:String?) {

        let alertController = UIAlertController(title: nil, message:message, preferredStyle: UIAlertController.Style.alert)
        let alertAction = UIAlertAction(title: NSLocalizedString("OK", comment: "OK"), style: UIAlertAction.Style.default) { (alertAction) in

            //                if let strongSelf = self
            //                {
            //                    strongSelf.startScan()
            //                }
        }

        alertController.addAction(alertAction)
        present(alertController, animated: true, completion: nil)
    }

    //MARK: - Image Permisson Methods
    func cameraSelected() {
        // First we check if the device has a camera (otherwise will crash in Simulator - also, some iPod touch models do not have a camera).
        let cameraMediaType = AVMediaType.video
        let cameraAuthorizationStatus = AVCaptureDevice.authorizationStatus(for: cameraMediaType)

        switch cameraAuthorizationStatus {
        case .denied:
            print("Access Denied")
            //            $(PRODUCT_NAME) requires camera to scan barcode.
            let alert = UIAlertController(title: "MSEDCL Chargers Would Like To Access the Camera", message: "Please grant camera permission to scan barcode", preferredStyle: .alert )
            alert.addAction(UIAlertAction(title: "Open Settings", style: .default) { alert in
                if let appSettingsURL = NSURL(string: UIApplication.openSettingsURLString) {
//                    UIApplication.shared.openURL(appSettingsURL as URL)
                    UIApplication.shared.open(appSettingsURL as URL)
                }
            })
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel) { alert in
                //                if let appSettingsURL = NSURL(string: UIApplication.openSettingsURLString) {
                //                    UIApplication.shared.openURL(appSettingsURL as URL)
                //                }
            })
            present(alert, animated: true, completion: nil)
        case .authorized:
            print("authorized")
        //            AVCaptureDevice.requestAccess(for: cameraMediaType) { granted in
        //                if granted {
        //                    print("Granted access to \(cameraMediaType)")
        //                } else {
        //                    print("Denied access to \(cameraMediaType)")
        //                }
        //            }
        case .restricted:
            print("restricted")
        //            AVCaptureDevice.requestAccess(for: cameraMediaType) { granted in
        //                if granted {
        //                    print("Granted access to \(cameraMediaType)")
        //                } else {
        //                    print("Denied access to \(cameraMediaType)")
        //                }
        //            }

        case .notDetermined:
            // Prompting user for the permission to use the camera.
            AVCaptureDevice.requestAccess(for: cameraMediaType) { granted in
                if granted {
                    print("Granted access to \(cameraMediaType)")
                } else {
                    print("Denied access to \(cameraMediaType)")
                }
            }
        @unknown default:
            print("Granted access to")
        }
    }
}

extension ScanQRVC: QRScannerCodeDelegate {
    func qrCodeScanningDidCompleteWithResult(result: String) {
        print(result)
    }

    func qrCodeScanningFailedWithError(error: String) {
        print(error)
    }

    func qrScanner(_ controller: UIViewController, scanDidComplete result: String) {
        print("result:\(result)")
    }

    func qrScannerDidFail(_ controller: UIViewController, error: String) {
        print("error:\(error)")
    }

    func qrScannerDidCancel(_ controller: UIViewController) {
        print("SwiftQRScanner did cancel")
    }
}




